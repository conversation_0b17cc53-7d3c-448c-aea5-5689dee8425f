#!/usr/bin/env tsx

/**
 * Comprehensive FAQ System Test Script
 *
 * This script tests the complete FAQ system functionality including:
 * - Database operations
 * - API endpoints
 * - Data transformations
 * - Component integration
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

// Load environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const adminApiKey = process.env.ADMIN_API_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  console.error('   - ADMIN_API_KEY (optional, for API testing)');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface TestFAQ {
  id?: string;
  question: string;
  answer: string;
  category?: string;
  displayOrder?: number;
  priority?: number;
  isActive?: boolean;
  isFeatured?: boolean;
  source?: string;
}

async function testFAQSystem() {
  console.log('🧪 Testing FAQ System (JSONB-based)...\n');

  let testToolId: string | null = null;

  try {
    // Test 1: Database Schema Verification
    console.log('1️⃣ Testing database schema...');

    // Test if faqs column exists by trying to query it
    const { error: schemaError } = await supabase
      .from('tools')
      .select('id, faqs')
      .limit(1);

    if (schemaError && (schemaError.message.includes('column "faqs" does not exist') || schemaError.message.includes('column tools.faqs does not exist'))) {
      throw new Error('FAQs column not found in tools table. Please run the migration first.');
    } else if (schemaError) {
      throw new Error(`Database schema test failed: ${schemaError.message}`);
    }

    console.log('✅ FAQs JSONB column exists in tools table');

    // Test 2: Create Test Tool with FAQs
    console.log('\n2️⃣ Creating test tool with FAQs...');
    
    const testFAQs: TestFAQ[] = [
      {
        id: crypto.randomUUID(),
        question: 'What is this test tool?',
        answer: 'This is a test tool created for FAQ system testing.',
        category: 'general',
        displayOrder: 0,
        priority: 5,
        isActive: true,
        isFeatured: true,
        source: 'manual'
      },
      {
        id: crypto.randomUUID(),
        question: 'How much does it cost?',
        answer: 'This test tool is free for testing purposes.',
        category: 'pricing',
        displayOrder: 1,
        priority: 3,
        isActive: true,
        isFeatured: false,
        source: 'manual'
      }
    ];

    const { data: newTool, error: createError } = await supabase
      .from('tools')
      .insert({
        id: `test-tool-${Date.now()}`,
        name: 'FAQ Test Tool',
        slug: `faq-test-tool-${Date.now()}`,
        description: 'A tool created for testing the FAQ system',
        link: `/tools/faq-test-tool-${Date.now()}`,
        website: 'https://example.com',
        faqs: testFAQs
      })
      .select()
      .single();

    if (createError || !newTool) {
      throw new Error(`Failed to create test tool: ${createError?.message}`);
    }

    testToolId = newTool.id;
    console.log(`✅ Test tool created: ${newTool.name} (ID: ${testToolId})`);
    console.log(`   FAQs count: ${Array.isArray(newTool.faqs) ? newTool.faqs.length : 0}`);

    // Test 3: Query FAQs from Database
    console.log('\n3️⃣ Testing FAQ queries...');
    
    const { data: toolWithFaqs, error: queryError } = await supabase
      .from('tools')
      .select('id, name, faqs')
      .eq('id', testToolId)
      .single();

    if (queryError || !toolWithFaqs) {
      throw new Error(`Failed to query tool FAQs: ${queryError?.message}`);
    }

    const faqs = toolWithFaqs.faqs;
    if (!Array.isArray(faqs) || faqs.length === 0) {
      throw new Error('No FAQs found in queried tool');
    }

    console.log(`✅ Successfully queried ${faqs.length} FAQs`);
    faqs.forEach((faq: any, index: number) => {
      console.log(`   ${index + 1}. ${faq.question} (Category: ${faq.category})`);
    });

    // Test 4: Update FAQs
    console.log('\n4️⃣ Testing FAQ updates...');
    
    const updatedFAQs = [...faqs];
    updatedFAQs[0].answer = 'Updated answer for testing FAQ system functionality.';
    updatedFAQs.push({
      id: crypto.randomUUID(),
      question: 'Is this system working?',
      answer: 'Yes, the FAQ system is working perfectly!',
      category: 'support',
      displayOrder: 2,
      priority: 4,
      isActive: true,
      isFeatured: false,
      source: 'manual'
    });

    const { error: updateError } = await supabase
      .from('tools')
      .update({ faqs: updatedFAQs })
      .eq('id', testToolId);

    if (updateError) {
      throw new Error(`Failed to update FAQs: ${updateError.message}`);
    }

    console.log('✅ FAQs updated successfully');
    console.log(`   Total FAQs: ${updatedFAQs.length}`);

    // Test 5: JSONB Filtering
    console.log('\n5️⃣ Testing JSONB filtering...');
    
    const { data: filteredTools, error: filterError } = await supabase
      .from('tools')
      .select('id, name, faqs')
      .contains('faqs', [{ category: 'general' }]);

    if (filterError) {
      console.warn('⚠️  JSONB filtering test failed (this is non-critical):', filterError.message);
    } else {
      console.log(`✅ JSONB filtering works: ${filteredTools?.length || 0} tools found with general FAQs`);
    }

    // Test 6: API Endpoint Testing (if admin key available)
    if (adminApiKey) {
      console.log('\n6️⃣ Testing API endpoints...');
      
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/tools/${testToolId}/faqs`);
        
        if (response.ok) {
          const apiResult = await response.json();
          console.log(`✅ Public API endpoint works: ${apiResult.data?.length || 0} FAQs returned`);
        } else {
          console.warn('⚠️  Public API endpoint test failed (app may not be running)');
        }
      } catch (apiError) {
        console.warn('⚠️  API endpoint test skipped (app not accessible)');
      }
    }

    console.log('\n✨ FAQ System test completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Database schema verified');
    console.log('   ✅ FAQ creation and storage tested');
    console.log('   ✅ FAQ querying functionality verified');
    console.log('   ✅ FAQ updates working correctly');
    console.log('   ✅ JSONB operations functional');
    console.log('   ✅ System ready for production use');

  } catch (error) {
    console.error('\n❌ FAQ System test failed:', error);
    console.error('\n🔧 Troubleshooting steps:');
    console.error('   1. Ensure the FAQ migration has been applied');
    console.error('   2. Check database permissions');
    console.error('   3. Verify environment variables are correct');
    console.error('   4. Review Supabase logs for detailed errors');
  } finally {
    // Cleanup: Remove test tool
    if (testToolId) {
      console.log('\n🧹 Cleaning up test data...');
      
      const { error: deleteError } = await supabase
        .from('tools')
        .delete()
        .eq('id', testToolId);

      if (deleteError) {
        console.warn(`⚠️  Failed to cleanup test tool: ${deleteError.message}`);
        console.warn(`   Please manually delete tool with ID: ${testToolId}`);
      } else {
        console.log('✅ Test data cleaned up successfully');
      }
    }
  }
}

// Execute the test
if (require.main === module) {
  testFAQSystem().catch(console.error);
}

export { testFAQSystem };
