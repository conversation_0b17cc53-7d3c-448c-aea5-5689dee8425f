-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Categories table
CREATE TABLE categories (
  id VARCHAR(255) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  icon_name VARCHAR(100),
  description TEXT,
  meta_title VARCHAR(255),
  meta_description TEXT,
  color_class VARCHAR(100),
  text_color_class VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tools table
CREATE TABLE tools (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  logo_url TEXT,
  description TEXT,
  short_description VARCHAR(150),
  detailed_description TEXT,
  link TEXT NOT NULL,
  website TEXT,
  category_id VARCHAR(255) REFERENCES categories(id),
  subcategory VARCHAR(255),
  company VA<PERSON>HA<PERSON>(255),
  is_verified BOOLEAN DEFAULT FALSE,
  is_claimed BOOLEAN DEFAULT FALSE,
  features <PERSON><PERSON><PERSON><PERSON>,
  screenshots JSON<PERSON>,
  pricing JSONB,
  social_links JSONB,
  pros_and_cons JSONB,
  haiku JSONB,
  hashtags JSONB,
  releases JSONB,
  claim_info JSONB,
  meta_title VARCHAR(255),
  meta_description TEXT,

  -- Content Generation & Workflow
  content_status VARCHAR(20) DEFAULT 'draft' CHECK (content_status IN ('draft', 'published', 'archived')),
  generated_content JSONB,

  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP
);

-- Reviews table
CREATE TABLE reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tool_id VARCHAR(255) REFERENCES tools(id),
  user_name VARCHAR(255) NOT NULL,
  user_email VARCHAR(255),
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  title VARCHAR(255),
  content TEXT NOT NULL,
  is_approved BOOLEAN DEFAULT FALSE,
  helpful_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tool submissions table
CREATE TABLE tool_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  url TEXT NOT NULL,
  description TEXT NOT NULL,
  category VARCHAR(255),
  subcategory VARCHAR(255),
  submitter_name VARCHAR(255),
  submitter_email VARCHAR(255) NOT NULL,
  logo_url TEXT,
  tags JSONB,
  pricing_type VARCHAR(100),
  status VARCHAR(50) DEFAULT 'pending',
  review_notes TEXT,
  submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  reviewed_at TIMESTAMP
);

-- Tags table (for flexible tagging)
CREATE TABLE tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) UNIQUE NOT NULL,
  type VARCHAR(50), -- 'Trending', 'New', 'Premium', etc.
  color VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tool tags junction table
CREATE TABLE tool_tags (
  tool_id VARCHAR(255) REFERENCES tools(id),
  tag_id UUID REFERENCES tags(id),
  PRIMARY KEY (tool_id, tag_id)
);

-- Create indexes for better performance
CREATE INDEX idx_tools_category_id ON tools(category_id);
CREATE INDEX idx_tools_content_status ON tools(content_status);
CREATE INDEX idx_tools_created_at ON tools(created_at);
CREATE INDEX idx_tools_slug ON tools(slug);
CREATE INDEX idx_reviews_tool_id ON reviews(tool_id);
CREATE INDEX idx_reviews_is_approved ON reviews(is_approved);
CREATE INDEX idx_tool_submissions_status ON tool_submissions(status);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tools_updated_at BEFORE UPDATE ON tools FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tools ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE tool_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE tool_tags ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY "Categories are viewable by everyone" ON categories FOR SELECT USING (true);
CREATE POLICY "Published tools are viewable by everyone" ON tools FOR SELECT USING (content_status = 'published');
CREATE POLICY "Approved reviews are viewable by everyone" ON reviews FOR SELECT USING (is_approved = true);
CREATE POLICY "Tags are viewable by everyone" ON tags FOR SELECT USING (true);
CREATE POLICY "Tool tags are viewable by everyone" ON tool_tags FOR SELECT USING (true);

-- Create policies for submissions (anyone can submit)
CREATE POLICY "Anyone can submit tools" ON tool_submissions FOR INSERT WITH CHECK (true);

-- Create policies for reviews (anyone can submit, but they need approval)
CREATE POLICY "Anyone can submit reviews" ON reviews FOR INSERT WITH CHECK (true);
