'use client';

import React from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

/**
 * Test page to verify admin layout and navigation functionality
 * This page demonstrates the admin layout components working together
 */
export default function AdminLayoutTestPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Layout Test Page</h1>
        <p className="text-gray-400 mt-1">Testing admin layout and navigation components</p>
      </div>

      {/* Test Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-3">Navigation Test</h3>
          <p className="text-gray-400 mb-4">
            The sidebar navigation should be visible and functional. Try clicking different navigation items.
          </p>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Sidebar visible</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Navigation functional</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Mobile responsive</span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-3">Header Test</h3>
          <p className="text-gray-400 mb-4">
            The header should display notifications, user menu, and system status.
          </p>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Notifications working</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">User menu functional</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">System status visible</span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-3">Breadcrumbs Test</h3>
          <p className="text-gray-400 mb-4">
            Breadcrumbs should show the current page path and allow navigation.
          </p>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Path displayed correctly</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Navigation links work</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Responsive design</span>
            </div>
          </div>
        </Card>
      </div>

      {/* Authentication Test */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-white mb-3">Authentication Test</h3>
        <p className="text-gray-400 mb-4">
          If you can see this page, authentication is working correctly. The layout should have validated admin access.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-white">Authentication Status</h4>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Admin access validated</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Route protection active</span>
            </div>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-white">Layout Features</h4>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">Design system applied</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-300">TypeScript types working</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Interactive Elements Test */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-white mb-3">Interactive Elements Test</h3>
        <p className="text-gray-400 mb-4">
          Test various interactive elements to ensure they follow the design system.
        </p>
        <div className="space-y-4">
          <div className="flex flex-wrap gap-3">
            <Button variant="primary">Primary Button</Button>
            <Button variant="secondary">Secondary Button</Button>
            <button
              className="text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
              style={{
                backgroundColor: 'rgb(255, 150, 0)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
              }}
            >
              Custom Orange Button
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-zinc-700 p-4 rounded-lg">
              <h5 className="text-white font-medium mb-2">Zinc-700 Background</h5>
              <p className="text-gray-300 text-sm">Secondary background color</p>
            </div>
            <div className="bg-zinc-800 border border-black p-4 rounded-lg">
              <h5 className="text-white font-medium mb-2">Card Background</h5>
              <p className="text-gray-300 text-sm">Primary card background with border</p>
            </div>
            <div className="bg-zinc-900 p-4 rounded-lg">
              <h5 className="text-white font-medium mb-2">Page Background</h5>
              <p className="text-gray-300 text-sm">Main page background color</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Footer Test */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-white mb-3">Layout Structure Test</h3>
        <p className="text-gray-400 mb-4">
          Verify that all layout components are properly structured and styled.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-white mb-3">Layout Components</h4>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Admin Layout Wrapper</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Sidebar Navigation</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Header with Actions</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Breadcrumb Navigation</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Main Content Area</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Footer Information</span>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-white mb-3">Design System</h4>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Dark theme (bg-zinc-900)</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Black borders (#151515)</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Card backgrounds (#2a2b31)</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Orange hover effects</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Roboto font family</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">Responsive design</span>
              </li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
}
