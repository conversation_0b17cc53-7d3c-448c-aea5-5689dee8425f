'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { DbCategory } from '@/lib/types';
import { apiClient } from '@/lib/api';

// Validation schema for Category Form
const categorySchema = z.object({
  title: z.string().min(1, 'Category title is required').max(255, 'Title must be less than 255 characters'),
  icon_name: z.string().max(100, 'Icon name must be less than 100 characters').optional().or(z.literal('')),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional().or(z.literal('')),
  meta_title: z.string().max(255, 'Meta title must be less than 255 characters').optional().or(z.literal('')),
  meta_description: z.string().max(500, 'Meta description must be less than 500 characters').optional().or(z.literal('')),
  color_class: z.string().max(100, 'Color class must be less than 100 characters').optional().or(z.literal('')),
  text_color_class: z.string().max(100, 'Text color class must be less than 100 characters').optional().or(z.literal('')),
});

type CategoryFormData = z.infer<typeof categorySchema>;

interface CategoryFormProps {
  category?: DbCategory;
  onSuccess?: (categoryId: string) => void;
  onCancel?: () => void;
  mode: 'create' | 'edit';
}

export function CategoryForm({ category, onSuccess, onCancel, mode }: CategoryFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    mode: 'onChange',
    defaultValues: category ? {
      title: category.title || '',
      icon_name: category.icon_name || '',
      description: category.description || '',
      meta_title: category.meta_title || '',
      meta_description: category.meta_description || '',
      color_class: category.color_class || '',
      text_color_class: category.text_color_class || '',
    } : {
      title: '',
      icon_name: '',
      description: '',
      meta_title: '',
      meta_description: '',
      color_class: 'bg-blue-500 hover:bg-blue-400',
      text_color_class: 'text-white',
    }
  });

  const onSubmit = async (data: CategoryFormData) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Get admin API key from environment or session
      const adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'admin-dashboard-access';

      let result: DbCategory;
      
      if (mode === 'create') {
        // Create new category
        result = await apiClient.createAdminCategory(data, adminApiKey);
      } else {
        // Update existing category
        if (!category?.id) {
          throw new Error('Category ID is required for updates');
        }
        result = await apiClient.updateAdminCategory(category.id, data, adminApiKey);
      }
      
      setSuccess(true);
      reset();

      if (onSuccess && result.id) {
        setTimeout(() => onSuccess(result.id), 2000);
      }
    } catch (err) {
      setSubmitError(err instanceof Error ? err.message : `Failed to ${mode} category`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
        <div className="text-center">
          <div className="text-green-500 text-4xl mb-4">✓</div>
          <h3 className="text-xl font-semibold text-white mb-2">
            Category {mode === 'create' ? 'Created' : 'Updated'} Successfully!
          </h3>
          <p className="text-gray-300">
            The category has been {mode === 'create' ? 'created' : 'updated'} and is now available.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
      <h2 className="text-2xl font-bold text-white mb-6 font-roboto">
        {mode === 'create' ? 'Create New Category' : 'Edit Category'}
      </h2>

      {submitError && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
          <strong>Error:</strong> {submitError}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">Basic Information</h3>
          
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-white mb-2">
              Category Title *
            </label>
            <input
              {...register('title')}
              type="text"
              id="title"
              className="w-full px-3 py-2 bg-zinc-800 border border-black text-white rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="e.g., AI Writing Tools"
            />
            {errors.title && (
              <p className="text-red-400 text-sm mt-1">{errors.title.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="icon_name" className="block text-sm font-medium text-white mb-2">
              Icon Name
            </label>
            <input
              {...register('icon_name')}
              type="text"
              id="icon_name"
              className="w-full px-3 py-2 bg-zinc-800 border border-black text-white rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="e.g., FileText, Grid, Zap"
            />
            {errors.icon_name && (
              <p className="text-red-400 text-sm mt-1">{errors.icon_name.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-white mb-2">
              Description
            </label>
            <textarea
              {...register('description')}
              id="description"
              rows={3}
              className="w-full px-3 py-2 bg-zinc-800 border border-black text-white rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="Brief description of this category..."
            />
            {errors.description && (
              <p className="text-red-400 text-sm mt-1">{errors.description.message}</p>
            )}
          </div>
        </div>

        {/* Styling */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">Styling</h3>
          
          <div>
            <label htmlFor="color_class" className="block text-sm font-medium text-white mb-2">
              Color Classes
            </label>
            <input
              {...register('color_class')}
              type="text"
              id="color_class"
              className="w-full px-3 py-2 bg-zinc-800 border border-black text-white rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="e.g., bg-blue-500 hover:bg-blue-400"
            />
            {errors.color_class && (
              <p className="text-red-400 text-sm mt-1">{errors.color_class.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="text_color_class" className="block text-sm font-medium text-white mb-2">
              Text Color Classes
            </label>
            <input
              {...register('text_color_class')}
              type="text"
              id="text_color_class"
              className="w-full px-3 py-2 bg-zinc-800 border border-black text-white rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="e.g., text-white"
            />
            {errors.text_color_class && (
              <p className="text-red-400 text-sm mt-1">{errors.text_color_class.message}</p>
            )}
          </div>
        </div>

        {/* SEO Metadata */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">SEO Metadata</h3>
          
          <div>
            <label htmlFor="meta_title" className="block text-sm font-medium text-white mb-2">
              Meta Title
            </label>
            <input
              {...register('meta_title')}
              type="text"
              id="meta_title"
              className="w-full px-3 py-2 bg-zinc-800 border border-black text-white rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="SEO title for this category"
            />
            {errors.meta_title && (
              <p className="text-red-400 text-sm mt-1">{errors.meta_title.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="meta_description" className="block text-sm font-medium text-white mb-2">
              Meta Description
            </label>
            <textarea
              {...register('meta_description')}
              id="meta_description"
              rows={2}
              className="w-full px-3 py-2 bg-zinc-800 border border-black text-white rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="SEO description for this category"
            />
            {errors.meta_description && (
              <p className="text-red-400 text-sm mt-1">{errors.meta_description.message}</p>
            )}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex space-x-4 pt-6">
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex-1 bg-orange-500 hover:bg-orange-600 disabled:bg-orange-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            {isSubmitting ? (mode === 'create' ? 'Creating...' : 'Updating...') : (mode === 'create' ? 'Create Category' : 'Update Category')}
          </button>
          
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 bg-zinc-700 hover:bg-zinc-600 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Cancel
            </button>
          )}
        </div>
      </form>
    </div>
  );
}
