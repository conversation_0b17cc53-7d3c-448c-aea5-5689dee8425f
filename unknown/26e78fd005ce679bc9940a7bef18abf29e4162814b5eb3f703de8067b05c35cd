import { NextRequest, NextResponse } from 'next/server';
import { getJobQueue, getJobManager } from '@/lib/jobs';
import { JobType, JobPriority } from '@/lib/jobs/types';
import { validate<PERSON><PERSON><PERSON>ey } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { url, options = {}, priority = 'normal', async: isAsync = false } = await request.json();

    if (!url) {
      return NextResponse.json(
        { success: false, error: 'URL is required' },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      new URL(url);
    } catch {
      return NextResponse.json(
        { success: false, error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    const queue = getJobQueue();
    
    // Create web scraping job
    const job = await queue.add(
      JobType.WEB_SCRAPING,
      {
        url,
        options: {
          timeout: options.timeout || 30000,
          waitForSelector: options.waitForSelector,
          extractImages: options.extractImages !== false, // default true
          extractLinks: options.extractLinks !== false,   // default true
          ...options,
        },
      },
      {
        priority: priority === 'high' ? JobPriority.HIGH : 
                 priority === 'urgent' ? JobPriority.URGENT : 
                 priority === 'low' ? JobPriority.LOW : JobPriority.NORMAL,
      }
    );

    if (isAsync) {
      // Return immediately with job ID
      return NextResponse.json({
        success: true,
        data: {
          jobId: job.id,
          status: job.status,
          message: 'Scraping job started',
        },
      });
    } else {
      // Wait for job completion (with timeout)
      const result = await waitForJobCompletion(job.id, 60000); // 60 second timeout
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          data: result.data,
        });
      } else {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 500 }
        );
      }
    }
  } catch (error) {
    console.error('Error starting scraping job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to start scraping job' },
      { status: 500 }
    );
  }
}

async function waitForJobCompletion(jobId: string, timeout: number): Promise<{ success: boolean; data?: any; error?: string }> {
  const queue = getJobQueue();
  const startTime = Date.now();
  
  return new Promise((resolve) => {
    const checkJob = async () => {
      try {
        const job = await queue.getJob(jobId);
        
        if (!job) {
          resolve({ success: false, error: 'Job not found' });
          return;
        }

        if (job.status === 'completed') {
          resolve({ success: true, data: job.result });
          return;
        }

        if (job.status === 'failed') {
          resolve({ success: false, error: job.error || 'Job failed' });
          return;
        }

        // Check timeout
        if (Date.now() - startTime > timeout) {
          resolve({ success: false, error: 'Job timeout' });
          return;
        }

        // Continue checking
        setTimeout(checkJob, 2000); // Check every 2 seconds
      } catch (error) {
        resolve({ success: false, error: 'Error checking job status' });
      }
    };

    checkJob();
  });
}
