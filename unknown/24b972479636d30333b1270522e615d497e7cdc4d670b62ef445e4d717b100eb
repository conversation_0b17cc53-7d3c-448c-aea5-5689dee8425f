#!/usr/bin/env tsx

/**
 * End-to-End Testing Script
 * 
 * Tests the complete tool submission workflow:
 * 1. Submit tool via API
 * 2. Monitor job processing
 * 3. Verify content generation
 * 4. Check database storage
 * 5. Validate email notifications
 */

import { createClient } from '@supabase/supabase-js';
import { JobManager } from '../src/lib/jobs/job-manager';
import { JobStatus } from '../src/lib/jobs/types';

interface TestResult {
  step: string;
  status: 'pass' | 'fail' | 'skip';
  message: string;
  duration: number;
  details?: any;
}

class EndToEndTester {
  private results: TestResult[] = [];
  private supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );
  private baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  private jobManager = new JobManager();

  async runFullTest(): Promise<void> {
    console.log('🧪 Starting End-to-End Testing...\n');

    await this.testToolSubmission();
    await this.testJobProcessing();
    await this.testContentGeneration();
    await this.testDatabaseStorage();
    await this.testEmailNotifications();
    await this.testAPIEndpoints();

    this.printResults();
  }

  private async testToolSubmission(): Promise<void> {
    const start = Date.now();
    
    try {
      const testSubmission = {
        url: 'https://openai.com',
        name: 'OpenAI Test Tool',
        description: 'Test submission for E2E testing',
        category: 'ai-development',
        submitterEmail: '<EMAIL>',
        submitterName: 'E2E Test User'
      };

      const response = await fetch(`${this.baseUrl}/api/submissions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testSubmission),
      });

      const duration = Date.now() - start;
      const data = await response.json();

      if (response.ok && data.success) {
        this.results.push({
          step: 'Tool Submission',
          status: 'pass',
          message: `Tool submitted successfully (Job ID: ${data.jobId})`,
          duration,
          details: { jobId: data.jobId, submissionId: data.submissionId }
        });
      } else {
        this.results.push({
          step: 'Tool Submission',
          status: 'fail',
          message: `Submission failed: ${data.error || 'Unknown error'}`,
          duration,
          details: { response: data }
        });
      }
    } catch (error) {
      const duration = Date.now() - start;
      this.results.push({
        step: 'Tool Submission',
        status: 'fail',
        message: `Submission request failed: ${error}`,
        duration,
        details: { error }
      });
    }
  }

  private async testJobProcessing(): Promise<void> {
    const start = Date.now();
    
    try {
      if (process.env.JOB_QUEUE_ENABLED !== 'true') {
        this.results.push({
          step: 'Job Processing',
          status: 'skip',
          message: 'Job queue is disabled',
          duration: Date.now() - start,
        });
        return;
      }

      // Wait for jobs to be processed (max 60 seconds)
      let attempts = 0;
      const maxAttempts = 60;
      let jobsCompleted = false;

      while (attempts < maxAttempts && !jobsCompleted) {
        const jobs = await this.jobManager.getJobs();
        const pendingJobs = jobs.filter(job =>
          job.status === JobStatus.PENDING || job.status === JobStatus.PROCESSING
        );

        if (pendingJobs.length === 0) {
          jobsCompleted = true;
          break;
        }

        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
      }

      const duration = Date.now() - start;
      const allJobs = await this.jobManager.getJobs();
      const recentJobs = allJobs.filter(job =>
        job.createdAt.getTime() > start - 10000 // Jobs from last 10 seconds before test
      );

      if (jobsCompleted) {
        const completedJobs = recentJobs.filter(job => job.status === JobStatus.COMPLETED);
        const failedJobs = recentJobs.filter(job => job.status === JobStatus.FAILED);

        if (failedJobs.length > 0) {
          this.results.push({
            step: 'Job Processing',
            status: 'fail',
            message: `${failedJobs.length} jobs failed during processing`,
            duration,
            details: { 
              completed: completedJobs.length,
              failed: failedJobs.length,
              failedJobs: failedJobs.map(j => ({ id: j.id, error: j.error }))
            }
          });
        } else {
          this.results.push({
            step: 'Job Processing',
            status: 'pass',
            message: `All jobs processed successfully (${completedJobs.length} completed)`,
            duration,
            details: { completedJobs: completedJobs.length }
          });
        }
      } else {
        this.results.push({
          step: 'Job Processing',
          status: 'fail',
          message: `Jobs did not complete within ${maxAttempts} seconds`,
          duration,
          details: { 
            totalJobs: allJobs.length,
            recentJobs: recentJobs.length,
            timeout: maxAttempts
          }
        });
      }
    } catch (error) {
      const duration = Date.now() - start;
      this.results.push({
        step: 'Job Processing',
        status: 'fail',
        message: `Job processing test failed: ${error}`,
        duration,
        details: { error }
      });
    }
  }

  private async testContentGeneration(): Promise<void> {
    const start = Date.now();
    
    try {
      if (process.env.CONTENT_GENERATION_ENABLED !== 'true') {
        this.results.push({
          step: 'Content Generation',
          status: 'skip',
          message: 'Content generation is disabled',
          duration: Date.now() - start,
        });
        return;
      }

      // Check if any tools were created with AI-generated content
      const { data: tools, error } = await this.supabase
        .from('tools')
        .select('*')
        .eq('name', 'OpenAI Test Tool')
        .order('created_at', { ascending: false })
        .limit(1);

      const duration = Date.now() - start;

      if (error) {
        this.results.push({
          step: 'Content Generation',
          status: 'fail',
          message: `Database query failed: ${error.message}`,
          duration,
          details: { error }
        });
        return;
      }

      if (tools && tools.length > 0) {
        const tool = tools[0];
        const hasGeneratedContent = tool.description && 
                                   tool.detailed_description && 
                                   tool.features && 
                                   tool.pros && 
                                   tool.cons;

        if (hasGeneratedContent) {
          this.results.push({
            step: 'Content Generation',
            status: 'pass',
            message: 'AI content generated successfully',
            duration,
            details: {
              toolId: tool.id,
              hasDescription: !!tool.description,
              hasDetailedDescription: !!tool.detailed_description,
              hasFeatures: !!tool.features,
              hasPros: !!tool.pros,
              hasCons: !!tool.cons
            }
          });
        } else {
          this.results.push({
            step: 'Content Generation',
            status: 'fail',
            message: 'Tool created but missing AI-generated content',
            duration,
            details: { tool }
          });
        }
      } else {
        this.results.push({
          step: 'Content Generation',
          status: 'fail',
          message: 'No test tool found in database',
          duration,
        });
      }
    } catch (error) {
      const duration = Date.now() - start;
      this.results.push({
        step: 'Content Generation',
        status: 'fail',
        message: `Content generation test failed: ${error}`,
        duration,
        details: { error }
      });
    }
  }

  private async testDatabaseStorage(): Promise<void> {
    const start = Date.now();
    
    try {
      // Test database connectivity and data integrity
      const { data: categories, error: catError } = await this.supabase
        .from('categories')
        .select('count')
        .limit(1);

      const { data: tools, error: toolError } = await this.supabase
        .from('tools')
        .select('count')
        .limit(1);

      const { data: submissions, error: subError } = await this.supabase
        .from('tool_submissions')
        .select('count')
        .limit(1);

      const duration = Date.now() - start;

      if (catError || toolError || subError) {
        this.results.push({
          step: 'Database Storage',
          status: 'fail',
          message: 'Database queries failed',
          duration,
          details: {
            categoryError: catError?.message,
            toolError: toolError?.message,
            submissionError: subError?.message
          }
        });
      } else {
        this.results.push({
          step: 'Database Storage',
          status: 'pass',
          message: 'Database connectivity and queries working',
          duration,
          details: {
            categoriesAccessible: !!categories,
            toolsAccessible: !!tools,
            submissionsAccessible: !!submissions
          }
        });
      }
    } catch (error) {
      const duration = Date.now() - start;
      this.results.push({
        step: 'Database Storage',
        status: 'fail',
        message: `Database test failed: ${error}`,
        duration,
        details: { error }
      });
    }
  }

  private async testEmailNotifications(): Promise<void> {
    const start = Date.now();
    
    try {
      if (!process.env.SMTP_HOST || !process.env.ADMIN_EMAIL) {
        this.results.push({
          step: 'Email Notifications',
          status: 'skip',
          message: 'Email service not configured',
          duration: Date.now() - start,
        });
        return;
      }

      // Check if email jobs were created and processed
      const jobs = await this.jobManager.getJobs();
      const emailJobs = jobs.filter(job => job.type === 'email_notification');
      
      const duration = Date.now() - start;

      if (emailJobs.length > 0) {
        const completedEmailJobs = emailJobs.filter(job => job.status === JobStatus.COMPLETED);
        const failedEmailJobs = emailJobs.filter(job => job.status === JobStatus.FAILED);

        if (failedEmailJobs.length > 0) {
          this.results.push({
            step: 'Email Notifications',
            status: 'fail',
            message: `${failedEmailJobs.length} email jobs failed`,
            duration,
            details: {
              total: emailJobs.length,
              completed: completedEmailJobs.length,
              failed: failedEmailJobs.length
            }
          });
        } else {
          this.results.push({
            step: 'Email Notifications',
            status: 'pass',
            message: `Email notifications sent successfully (${completedEmailJobs.length} emails)`,
            duration,
            details: {
              total: emailJobs.length,
              completed: completedEmailJobs.length
            }
          });
        }
      } else {
        this.results.push({
          step: 'Email Notifications',
          status: 'fail',
          message: 'No email jobs found',
          duration,
        });
      }
    } catch (error) {
      const duration = Date.now() - start;
      this.results.push({
        step: 'Email Notifications',
        status: 'fail',
        message: `Email notification test failed: ${error}`,
        duration,
        details: { error }
      });
    }
  }

  private async testAPIEndpoints(): Promise<void> {
    const endpoints = [
      { path: '/api/categories', method: 'GET' },
      { path: '/api/tools', method: 'GET' },
      { path: '/api/tools?category=ai-development', method: 'GET' },
    ];

    for (const endpoint of endpoints) {
      const start = Date.now();
      
      try {
        const response = await fetch(`${this.baseUrl}${endpoint.path}`, {
          method: endpoint.method,
        });

        const duration = Date.now() - start;
        const data = await response.json();

        if (response.ok && data.success !== false) {
          this.results.push({
            step: `API ${endpoint.path}`,
            status: 'pass',
            message: `Endpoint responding correctly (${response.status})`,
            duration,
          });
        } else {
          this.results.push({
            step: `API ${endpoint.path}`,
            status: 'fail',
            message: `Endpoint returned error: ${data.error || response.statusText}`,
            duration,
            details: { status: response.status, data }
          });
        }
      } catch (error) {
        const duration = Date.now() - start;
        this.results.push({
          step: `API ${endpoint.path}`,
          status: 'fail',
          message: `Endpoint request failed: ${error}`,
          duration,
          details: { error }
        });
      }
    }
  }

  private printResults(): void {
    console.log('\n📊 End-to-End Test Results:\n');
    
    const passed = this.results.filter(r => r.status === 'pass').length;
    const failed = this.results.filter(r => r.status === 'fail').length;
    const skipped = this.results.filter(r => r.status === 'skip').length;

    this.results.forEach(result => {
      const icon = result.status === 'pass' ? '✅' : 
                   result.status === 'skip' ? '⏭️' : '❌';
      
      console.log(`${icon} ${result.step}: ${result.message} (${result.duration}ms)`);
      
      if (result.details && result.status === 'fail') {
        console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
      }
    });

    console.log(`\n📈 Summary: ${passed} passed, ${failed} failed, ${skipped} skipped`);
    
    if (failed > 0) {
      console.log('\n🚨 Some tests failed! Review issues before production deployment.');
      process.exit(1);
    } else {
      console.log('\n🎉 All tests passed! System is working correctly.');
      process.exit(0);
    }
  }
}

// Run E2E test if called directly
if (require.main === module) {
  const tester = new EndToEndTester();
  tester.runFullTest().catch(error => {
    console.error('E2E test failed:', error);
    process.exit(1);
  });
}

export { EndToEndTester };
