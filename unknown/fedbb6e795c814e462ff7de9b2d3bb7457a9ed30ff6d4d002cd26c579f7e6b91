#!/usr/bin/env tsx

/**
 * <PERSON><PERSON> script to create a web scraping job and then inspect the results
 * Run with: npx tsx scripts/demo-scraping.ts
 */

import { config } from 'dotenv';
import { JobManager } from '../src/lib/jobs/job-manager';
import { JobType, JobPriority } from '../src/lib/jobs/types';

// Load environment variables
config({ path: '.env.local' });

async function demoScraping() {
  console.log('🕷️ Demo: Web Scraping + Data Inspection\n');

  try {
    const jobManager = new JobManager();

    // Create a scraping job for a simple, reliable website
    console.log('📡 Creating web scraping job for example.com...');
    const job = await jobManager.createJob(
      JobType.WEB_SCRAPING,
      {
        url: 'https://example.com',
        options: {
          timeout: 15000,
          extractImages: true,
          extractLinks: true,
        },
      },
      { priority: JobPriority.HIGH }
    );

    console.log(`✅ Job created: ${job.id}`);
    console.log('⏳ Waiting for job to complete...\n');

    // Wait for job completion
    let attempts = 0;
    const maxAttempts = 20; // 40 seconds max wait

    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      
      const updatedJob = await jobManager.getJob(job.id);
      if (!updatedJob) {
        console.log('❌ Job not found');
        return;
      }

      console.log(`🔄 Status: ${updatedJob.status} (attempt ${attempts + 1}/${maxAttempts})`);

      if (updatedJob.status === 'completed') {
        console.log('✅ Job completed successfully!\n');
        
        // Display the scraped data
        if (updatedJob.result && updatedJob.result.data) {
          const data = updatedJob.result.data;
          
          console.log('🎉 SCRAPED DATA RESULTS:');
          console.log('=' .repeat(50));
          
          console.log(`📄 Title: ${data.title || 'N/A'}`);
          console.log(`🔗 URL: ${data.url || 'N/A'}`);
          console.log(`📝 Text Length: ${data.text ? data.text.length : 0} characters`);
          
          if (data.meta && Object.keys(data.meta).length > 0) {
            console.log('\n🏷️ META TAGS:');
            Object.entries(data.meta).slice(0, 5).forEach(([key, value]) => {
              console.log(`  ${key}: ${String(value).substring(0, 80)}`);
            });
          }
          
          if (data.headings && data.headings.length > 0) {
            console.log('\n📑 HEADINGS:');
            data.headings.forEach((heading: any) => {
              console.log(`  ${heading.level.toUpperCase()}: ${heading.text}`);
            });
          }
          
          if (data.text) {
            console.log('\n📖 TEXT CONTENT:');
            console.log(`"${data.text.substring(0, 300)}${data.text.length > 300 ? '...' : ''}"`);
          }
          
          if (updatedJob.result.screenshot) {
            console.log('\n📸 SCREENSHOT:');
            console.log(`  Size: ${Math.round(updatedJob.result.screenshot.length / 1024)} KB`);
            console.log(`  Format: PNG (base64 encoded)`);
          }
          
          console.log('\n' + '=' .repeat(50));
          console.log('✅ Demo completed! You can now run:');
          console.log('   npm run inspect:data - to see all scraped data');
          console.log('   powershell scripts/api-data-inspector.ps1 - for API inspection');
        } else {
          console.log('❌ No scraped data found in result');
        }
        return;
      } else if (updatedJob.status === 'failed') {
        console.log(`❌ Job failed: ${updatedJob.error}`);
        return;
      }

      attempts++;
    }

    console.log('⏰ Timeout waiting for job completion');
    
  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

demoScraping();
