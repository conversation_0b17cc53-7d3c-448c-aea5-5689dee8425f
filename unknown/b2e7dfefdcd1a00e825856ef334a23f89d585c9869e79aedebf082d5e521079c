'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { validateAdminAccess } from '@/lib/auth';
import { AdminSidebar } from '@/components/admin/AdminSidebar';
import { AdminHeader } from '@/components/admin/AdminHeader';
import { AdminBreadcrumbs } from '@/components/admin/AdminBreadcrumbs';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Authentication check
  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const isValid = await validateAdminAccess();
        setIsAuthenticated(isValid);
        
        if (!isValid) {
          setError('Unauthorized access. Admin privileges required.');
        }
      } catch (err) {
        console.error('Admin authentication error:', err);
        setError('Failed to validate admin access. Please try again.');
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthentication();
  }, []);

  // Handle sidebar toggle
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Close sidebar on route change (mobile)
  useEffect(() => {
    setIsSidebarOpen(false);
  }, [pathname]);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-white text-lg font-medium mt-4 font-roboto">
            Validating admin access...
          </p>
        </div>
      </div>
    );
  }

  // Unauthorized state
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg">
            <h1 className="text-2xl font-bold text-white mb-4 font-roboto">
              Access Denied
            </h1>
            <p className="text-gray-300 mb-6 font-roboto">
              {error || 'You need administrator privileges to access this area.'}
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={() => router.push('/')}
                className="bg-zinc-700 hover:bg-zinc-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                Return Home
              </button>
              <button
                onClick={() => window.location.reload()}
                className="text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
                style={{
                  backgroundColor: 'rgb(255, 150, 0)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
                }}
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Authenticated admin layout
  return (
    <div className="min-h-screen bg-zinc-900 text-white font-roboto">
      {/* Mobile sidebar overlay */}
      {isSidebarOpen && (
        <div
          className="admin-overlay lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <AdminSidebar
        isOpen={isSidebarOpen}
        onClose={() => setIsSidebarOpen(false)}
        currentPath={pathname}
      />

      {/* Main content area */}
      <div
        className="admin-main-content min-h-screen flex flex-col transition-all duration-300 ease-in-out"
        style={{ paddingTop: 'var(--admin-header-height)' }}
      >
        {/* Header */}
        <AdminHeader 
          onMenuClick={toggleSidebar}
          isSidebarOpen={isSidebarOpen}
        />

        {/* Breadcrumbs */}
        <AdminBreadcrumbs currentPath={pathname} />

        {/* Page content */}
        <main className="flex-1 p-6">
          <div className="mx-auto" style={{ maxWidth: 'var(--container-width)' }}>
            {children}
          </div>
        </main>

        {/* Footer */}
        <footer className="border-t border-zinc-800 p-4">
          <div className="mx-auto text-center text-sm text-gray-400" style={{ maxWidth: 'var(--container-width)' }}>
            <p>AI Dude Admin Panel &copy; 2024 - System Status: Online</p>
          </div>
        </footer>
      </div>
    </div>
  );
}
