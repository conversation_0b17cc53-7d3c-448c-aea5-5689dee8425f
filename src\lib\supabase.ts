import { createClient } from '@supabase/supabase-js';
import { AITool, DbTool, AICategory, DbCategory, ToolFilters, AdminToolFilters, PaginationInfo, FAQ, FAQFilters, FAQSource } from './types';

// Validate environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable');
}

if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable');
}

// Client for browser usage
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Admin client for server-side operations (only available server-side)
export const supabaseAdmin = supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey)
  : null;

// Transform database tool to frontend AITool type
function transformDbToolToAITool(dbTool: DbTool): AITool {
  // Helper function to safely parse JSON fields
  const safeJsonParse = (jsonString: any) => {
    if (!jsonString) return undefined;
    if (typeof jsonString === 'object') return jsonString; // Already parsed
    try {
      return JSON.parse(jsonString);
    } catch {
      return undefined;
    }
  };

  return {
    // Core fields - map database snake_case to frontend camelCase
    id: dbTool.id,
    name: dbTool.name,
    slug: dbTool.slug,
    logoUrl: dbTool.logo_url || '',
    description: dbTool.description || '',
    shortDescription: dbTool.short_description,
    detailedDescription: dbTool.detailed_description,
    link: dbTool.link,
    website: dbTool.website,
    category: dbTool.category_id || '',
    subcategory: dbTool.subcategory,
    company: dbTool.company,

    // Verification and claiming
    isVerified: dbTool.is_verified,
    isClaimed: dbTool.is_claimed,
    claimInfo: safeJsonParse(dbTool.claim_info),

    // Content fields
    features: safeJsonParse(dbTool.features),
    screenshots: safeJsonParse(dbTool.screenshots),
    pricing: safeJsonParse(dbTool.pricing),
    socialLinks: safeJsonParse(dbTool.social_links),
    prosAndCons: safeJsonParse(dbTool.pros_and_cons),
    releases: safeJsonParse(dbTool.releases),
    haiku: safeJsonParse(dbTool.haiku),
    hashtags: safeJsonParse(dbTool.hashtags),
    tags: Array.isArray(dbTool.hashtags) ? dbTool.hashtags.map((tag: string) => ({ type: tag as any })) : undefined,

    // SEO fields
    metaTitle: dbTool.meta_title,
    metaDescription: dbTool.meta_description,

    // Status and timestamps
    contentStatus: dbTool.content_status,
    generatedContent: safeJsonParse(dbTool.generated_content),
    createdAt: dbTool.created_at,
    updatedAt: dbTool.updated_at,
    publishedAt: dbTool.published_at,

    // Enhanced AI System fields
    scrapedData: safeJsonParse(dbTool.scraped_data),
    aiGenerationStatus: dbTool.ai_generation_status,
    lastScrapedAt: dbTool.last_scraped_at,
    editorialReviewId: dbTool.editorial_review_id,
    aiGenerationJobId: dbTool.ai_generation_job_id,
    submissionType: dbTool.submission_type,
    submissionSource: dbTool.submission_source,
    contentQualityScore: dbTool.content_quality_score,
    lastAiUpdate: dbTool.last_ai_update,

    // FAQ field
    faqs: transformDbFaqArrayToFAQArray(safeJsonParse(dbTool.faqs)),
  };
}

// Transform database category to frontend AICategory type
function transformDbCategoryToAICategory(dbCategory: any): AICategory {
  return {
    id: dbCategory.id || '',
    title: dbCategory.title || '',
    iconName: dbCategory.icon_name || 'Grid',
    description: dbCategory.description || '',
    tools: [], // Will be populated separately
    totalToolsCount: 0, // Will be calculated
    seeAllButton: {
      colorClass: dbCategory.color_class || 'bg-blue-500 hover:bg-blue-400',
      textColorClass: dbCategory.text_color_class || 'text-white',
    },
  };
}

// Transform JSONB FAQ array to frontend FAQ array
function transformDbFaqArrayToFAQArray(dbFaqArray: any[]): FAQ[] {
  if (!Array.isArray(dbFaqArray)) return [];

  return dbFaqArray.map((dbFaq: any) => ({
    id: dbFaq.id || crypto.randomUUID(), // Generate ID if not present
    question: dbFaq.question || '',
    answer: dbFaq.answer || '',
    displayOrder: dbFaq.displayOrder || dbFaq.display_order || 0,
    priority: dbFaq.priority || 0,
    category: dbFaq.category || 'general',
    tags: Array.isArray(dbFaq.tags) ? dbFaq.tags : undefined,
    isActive: dbFaq.isActive !== undefined ? dbFaq.isActive : (dbFaq.is_active !== undefined ? dbFaq.is_active : true),
    isFeatured: dbFaq.isFeatured !== undefined ? dbFaq.isFeatured : (dbFaq.is_featured !== undefined ? dbFaq.is_featured : false),
    source: dbFaq.source || 'manual',
    sourceMetadata: dbFaq.sourceMetadata || dbFaq.source_metadata,
    metaKeywords: dbFaq.metaKeywords || dbFaq.meta_keywords,
    helpScore: dbFaq.helpScore || dbFaq.help_score || 0,
    viewCount: dbFaq.viewCount || dbFaq.view_count || 0,
  }));
}

// Transform frontend FAQ array to JSONB FAQ array
function transformFAQArrayToDbFaqArray(faqArray: FAQ[]): any[] {
  if (!Array.isArray(faqArray)) return [];

  return faqArray.map((faq: FAQ) => ({
    id: faq.id || crypto.randomUUID(),
    question: faq.question,
    answer: faq.answer,
    displayOrder: faq.displayOrder || 0,
    priority: faq.priority || 0,
    category: faq.category || 'general',
    tags: faq.tags,
    isActive: faq.isActive !== undefined ? faq.isActive : true,
    isFeatured: faq.isFeatured || false,
    source: faq.source || 'manual',
    sourceMetadata: faq.sourceMetadata,
    metaKeywords: faq.metaKeywords,
    helpScore: faq.helpScore || 0,
    viewCount: faq.viewCount || 0,
  }));
}

// Database helper functions
export async function getTools(filters: ToolFilters = {}): Promise<{
  data: AITool[];
  pagination: PaginationInfo;
}> {
  let query = supabase
    .from('tools')
    .select('*', { count: 'exact' })
    .eq('content_status', 'published');

  // Apply filters
  if (filters.category) {
    query = query.eq('category_id', filters.category);
  }

  if (filters.subcategory) {
    query = query.eq('subcategory', filters.subcategory);
  }

  if (filters.search) {
    query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
  }

  if (filters.pricing) {
    query = query.eq('pricing->type', filters.pricing);
  }

  if (filters.verified !== undefined) {
    query = query.eq('is_verified', filters.verified);
  }

  // Apply sorting
  const sortBy = filters.sortBy || 'created_at';
  const sortOrder = filters.sortOrder || 'desc';
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination
  const page = filters.page || 1;
  const limit = filters.limit || 20;
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch tools: ${error.message}`);
  }

  return {
    data: data?.map(transformDbToolToAITool) || [],
    pagination: {
      currentPage: page,
      totalPages: Math.ceil((count || 0) / limit),
      totalItems: count || 0,
      itemsPerPage: limit,
    },
  };
}

// Admin-specific function to get all tools regardless of status
export async function getAdminTools(filters: AdminToolFilters = {}): Promise<{
  data: AITool[];
  pagination: PaginationInfo;
}> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  let query = supabaseAdmin
    .from('tools')
    .select('*', { count: 'exact' });

  // Apply admin-specific filters
  if (filters.category) {
    query = query.eq('category_id', filters.category);
  }

  if (filters.subcategory) {
    query = query.eq('subcategory', filters.subcategory);
  }

  if (filters.search) {
    query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
  }

  if (filters.pricing) {
    query = query.eq('pricing->type', filters.pricing);
  }

  if (filters.verified !== undefined) {
    query = query.eq('is_verified', filters.verified);
  }

  // Admin-specific filters
  if (filters.aiGenerationStatus) {
    query = query.eq('ai_generation_status', filters.aiGenerationStatus);
  }

  if (filters.contentStatus) {
    query = query.eq('content_status', filters.contentStatus);
  }

  if (filters.submissionType) {
    query = query.eq('submission_type', filters.submissionType);
  }

  if (filters.hasEditorialReview !== undefined) {
    if (filters.hasEditorialReview) {
      query = query.not('editorial_review_id', 'is', null);
    } else {
      query = query.is('editorial_review_id', null);
    }
  }

  if (filters.qualityScoreMin !== undefined) {
    query = query.gte('content_quality_score', filters.qualityScoreMin);
  }

  if (filters.qualityScoreMax !== undefined) {
    query = query.lte('content_quality_score', filters.qualityScoreMax);
  }

  if (filters.lastScrapedAfter) {
    query = query.gte('last_scraped_at', filters.lastScrapedAfter);
  }

  if (filters.lastScrapedBefore) {
    query = query.lte('last_scraped_at', filters.lastScrapedBefore);
  }

  // Apply sorting
  const sortBy = filters.sortBy || 'created_at';
  const sortOrder = filters.sortOrder || 'desc';
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination
  const page = filters.page || 1;
  const limit = filters.limit || 100; // Higher default limit for admin
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch admin tools: ${error.message}`);
  }

  return {
    data: data?.map(transformDbToolToAITool) || [],
    pagination: {
      currentPage: page,
      totalPages: Math.ceil((count || 0) / limit),
      totalItems: count || 0,
      itemsPerPage: limit,
    },
  };
}

export async function getToolById(id: string): Promise<AITool | null> {
  // Try to find by ID first
  let { data, error } = await supabase
    .from('tools')
    .select('*')
    .eq('id', id)
    .eq('content_status', 'published')
    .single();

  // If not found by ID, try to find by slug
  if (error && error.code === 'PGRST116') {
    ({ data, error } = await supabase
      .from('tools')
      .select('*')
      .eq('slug', id)
      .eq('content_status', 'published')
      .single());
  }

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // Tool not found
    }
    throw new Error(`Failed to fetch tool: ${error.message}`);
  }

  return data ? transformDbToolToAITool(data) : null;
}

export async function getCategories(): Promise<AICategory[]> {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('title');

  if (error) {
    throw new Error(`Failed to fetch categories: ${error.message}`);
  }

  return data?.map(transformDbCategoryToAICategory) || [];
}

export async function getCategoriesWithTools(limit: number = 15): Promise<AICategory[]> {
  // Get all categories
  const categories = await getCategories();

  // For each category, get its tools, prioritizing complete tools
  const categoriesWithTools = await Promise.all(
    categories.map(async (category) => {
      // Get all tools for the category and sort them manually
      const { data: allCategoryTools, error } = await supabase
        .from('tools')
        .select('*')
        .eq('category_id', category.id)
        .eq('content_status', 'published')
        .order('created_at', { ascending: false });

      if (error) {
        console.error(`Failed to fetch tools for category ${category.id}:`, error);
        return {
          ...category,
          tools: [],
          totalToolsCount: 0,
        };
      }

      // Sort tools: complete tools (with features) first, then others
      const sortedTools = (allCategoryTools || []).sort((a, b) => {
        const aHasFeatures = a.features ? 1 : 0;
        const bHasFeatures = b.features ? 1 : 0;

        // First sort by features (complete tools first)
        if (aHasFeatures !== bHasFeatures) {
          return bHasFeatures - aHasFeatures;
        }

        // Then sort by creation date (newest first)
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });

      // Take only the first 'limit' tools
      const limitedTools = sortedTools.slice(0, limit);
      const transformedTools = limitedTools?.map(transformDbToolToAITool) || [];



      return {
        ...category,
        tools: transformedTools,
        totalToolsCount: transformedTools.length,
      };
    })
  );

  return categoriesWithTools;
}

export async function getCategoryWithTools(categoryId: string): Promise<{
  category: AICategory;
  tools: AITool[];
}> {
  // Get category
  const { data: category, error: categoryError } = await supabase
    .from('categories')
    .select('*')
    .eq('id', categoryId)
    .single();

  if (categoryError) {
    throw new Error(`Failed to fetch category: ${categoryError.message}`);
  }

  // Get tools for category
  const { data: tools, error: toolsError } = await supabase
    .from('tools')
    .select('*')
    .eq('category_id', categoryId)
    .eq('content_status', 'published')
    .order('created_at', { ascending: false });

  if (toolsError) {
    throw new Error(`Failed to fetch tools: ${toolsError.message}`);
  }

  return {
    category: transformDbCategoryToAICategory(category),
    tools: tools?.map(transformDbToolToAITool) || [],
  };
}

// Category CRUD operations
export async function createCategory(categoryData: Partial<DbCategory>): Promise<DbCategory> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  // Generate a unique ID from the title
  const baseId = categoryData.title?.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '') || 'category';

  // Add timestamp to ensure uniqueness if needed
  const timestamp = Date.now();
  const uniqueId = `${baseId}-${timestamp}`;

  // Prepare the final data with the generated ID
  const finalData = {
    ...categoryData,
    id: uniqueId,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const { data, error } = await supabaseAdmin
    .from('categories')
    .insert([finalData])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create category: ${error.message}`);
  }

  return data;
}

export async function updateCategory(id: string, updates: Partial<DbCategory>): Promise<DbCategory> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  // Prepare updates with timestamp
  const dbUpdates = {
    ...updates,
    updated_at: new Date().toISOString()
  };

  const { data, error } = await supabaseAdmin
    .from('categories')
    .update(dbUpdates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to update category: ${error.message}`);
  }

  return data;
}

export async function deleteCategory(id: string): Promise<void> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  // First check if any tools are using this category
  const { data: toolsUsingCategory, error: checkError } = await supabaseAdmin
    .from('tools')
    .select('id, name')
    .eq('category_id', id)
    .limit(1);

  if (checkError) {
    throw new Error(`Failed to check category dependencies: ${checkError.message}`);
  }

  if (toolsUsingCategory && toolsUsingCategory.length > 0) {
    throw new Error(`Cannot delete category: ${toolsUsingCategory.length} tools are still using this category. Please reassign or delete those tools first.`);
  }

  const { error } = await supabaseAdmin
    .from('categories')
    .delete()
    .eq('id', id);

  if (error) {
    throw new Error(`Failed to delete category: ${error.message}`);
  }
}

export async function getCategoryById(id: string): Promise<DbCategory | null> {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // Category not found
    }
    throw new Error(`Failed to fetch category: ${error.message}`);
  }

  return data;
}

export async function getAdminCategories(): Promise<DbCategory[]> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  const { data, error } = await supabaseAdmin
    .from('categories')
    .select('*')
    .order('title');

  if (error) {
    throw new Error(`Failed to fetch admin categories: ${error.message}`);
  }

  return data || [];
}

export async function createTool(toolData: Partial<AITool>): Promise<AITool> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  // Generate a clean ID from the slug or name
  const baseId = toolData.slug || toolData.name?.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '') || 'tool';

  // Check if the base ID already exists
  let uniqueId = baseId;
  let counter = 1;

  while (true) {
    const { data: existingTool } = await supabaseAdmin
      .from('tools')
      .select('id')
      .eq('id', uniqueId)
      .single();

    if (!existingTool) {
      // ID is available
      break;
    }

    // ID exists, try with counter
    uniqueId = `${baseId}-${counter}`;
    counter++;

    // Safety check to prevent infinite loop
    if (counter > 1000) {
      throw new Error('Unable to generate unique tool ID after 1000 attempts');
    }
  }

  // Transform camelCase AITool fields to snake_case database fields
  const dbData: any = {
    id: uniqueId,
    slug: uniqueId, // Use the same clean ID as slug
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  // Map camelCase to snake_case for database insert
  if (toolData.name !== undefined) dbData.name = toolData.name;
  if (toolData.slug !== undefined) dbData.slug = toolData.slug;
  if (toolData.logoUrl !== undefined) dbData.logo_url = toolData.logoUrl;
  if (toolData.description !== undefined) dbData.description = toolData.description;
  if (toolData.shortDescription !== undefined) dbData.short_description = toolData.shortDescription;
  if (toolData.detailedDescription !== undefined) dbData.detailed_description = toolData.detailedDescription;
  if (toolData.link !== undefined) dbData.link = toolData.link;
  if (toolData.website !== undefined) dbData.website = toolData.website;
  if (toolData.category !== undefined) dbData.category_id = toolData.category;
  if (toolData.subcategory !== undefined) dbData.subcategory = toolData.subcategory;
  if (toolData.company !== undefined) dbData.company = toolData.company;
  if (toolData.isVerified !== undefined) dbData.is_verified = toolData.isVerified;
  if (toolData.isClaimed !== undefined) dbData.is_claimed = toolData.isClaimed;
  if (toolData.features !== undefined) dbData.features = toolData.features;
  if (toolData.screenshots !== undefined) dbData.screenshots = toolData.screenshots;
  if (toolData.pricing !== undefined) dbData.pricing = toolData.pricing;
  if (toolData.socialLinks !== undefined) dbData.social_links = toolData.socialLinks;
  if (toolData.prosAndCons !== undefined) dbData.pros_and_cons = toolData.prosAndCons;
  if (toolData.haiku !== undefined) dbData.haiku = toolData.haiku;
  if (toolData.hashtags !== undefined) dbData.hashtags = toolData.hashtags;
  if (toolData.releases !== undefined) dbData.releases = toolData.releases;
  if (toolData.claimInfo !== undefined) dbData.claim_info = toolData.claimInfo;
  if (toolData.metaTitle !== undefined) dbData.meta_title = toolData.metaTitle;
  if (toolData.metaDescription !== undefined) dbData.meta_description = toolData.metaDescription;
  if (toolData.contentStatus !== undefined) dbData.content_status = toolData.contentStatus;
  if (toolData.generatedContent !== undefined) dbData.generated_content = toolData.generatedContent;
  if (toolData.publishedAt !== undefined) dbData.published_at = toolData.publishedAt;

  // Enhanced AI system fields
  if (toolData.scrapedData !== undefined) dbData.scraped_data = toolData.scrapedData;
  if (toolData.aiGenerationStatus !== undefined) dbData.ai_generation_status = toolData.aiGenerationStatus;
  if (toolData.lastScrapedAt !== undefined) dbData.last_scraped_at = toolData.lastScrapedAt;
  if (toolData.editorialReviewId !== undefined) dbData.editorial_review_id = toolData.editorialReviewId;
  if (toolData.aiGenerationJobId !== undefined) dbData.ai_generation_job_id = toolData.aiGenerationJobId;
  if (toolData.submissionType !== undefined) dbData.submission_type = toolData.submissionType;
  if (toolData.submissionSource !== undefined) dbData.submission_source = toolData.submissionSource;
  if (toolData.contentQualityScore !== undefined) dbData.content_quality_score = toolData.contentQualityScore;
  if (toolData.lastAiUpdate !== undefined) dbData.last_ai_update = toolData.lastAiUpdate;

  // FAQ field
  if (toolData.faqs !== undefined) dbData.faqs = transformFAQArrayToDbFaqArray(toolData.faqs);

  const { data, error } = await supabaseAdmin
    .from('tools')
    .insert([dbData])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create tool: ${error.message}`);
  }

  return transformDbToolToAITool(data);
}

export async function updateTool(id: string, updates: Partial<AITool>, createVersion: boolean = true): Promise<AITool> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  // Create version before updating if requested
  if (createVersion) {
    try {
      const { VersionManager } = await import('@/lib/versioning/version-manager');
      const versionManager = new VersionManager();

      await versionManager.createVersion({
        toolId: id,
        changeSummary: 'Tool updated via admin interface',
        changeType: 'update',
        changeSource: 'admin_panel',
        createdBy: 'admin' // This should be passed from the calling context
      });
    } catch (versionError) {
      console.warn('Failed to create version before update:', versionError);
      // Don't fail the update if versioning fails
    }
  }

  // Transform camelCase AITool fields to snake_case database fields
  const dbUpdates: any = {
    updated_at: new Date().toISOString()
  };

  // Map camelCase to snake_case for database update
  if (updates.name !== undefined) dbUpdates.name = updates.name;
  if (updates.slug !== undefined) dbUpdates.slug = updates.slug;
  if (updates.logoUrl !== undefined) dbUpdates.logo_url = updates.logoUrl;
  if (updates.description !== undefined) dbUpdates.description = updates.description;
  if (updates.shortDescription !== undefined) dbUpdates.short_description = updates.shortDescription;
  if (updates.detailedDescription !== undefined) dbUpdates.detailed_description = updates.detailedDescription;
  if (updates.link !== undefined) dbUpdates.link = updates.link;
  if (updates.website !== undefined) dbUpdates.website = updates.website;
  if (updates.category !== undefined) dbUpdates.category_id = updates.category;
  if (updates.subcategory !== undefined) dbUpdates.subcategory = updates.subcategory;
  if (updates.company !== undefined) dbUpdates.company = updates.company;
  if (updates.isVerified !== undefined) dbUpdates.is_verified = updates.isVerified;
  if (updates.isClaimed !== undefined) dbUpdates.is_claimed = updates.isClaimed;
  if (updates.features !== undefined) dbUpdates.features = updates.features;
  if (updates.screenshots !== undefined) dbUpdates.screenshots = updates.screenshots;
  if (updates.pricing !== undefined) dbUpdates.pricing = updates.pricing;
  if (updates.socialLinks !== undefined) dbUpdates.social_links = updates.socialLinks;
  if (updates.prosAndCons !== undefined) dbUpdates.pros_and_cons = updates.prosAndCons;
  if (updates.haiku !== undefined) dbUpdates.haiku = updates.haiku;
  if (updates.hashtags !== undefined) dbUpdates.hashtags = updates.hashtags;
  if (updates.releases !== undefined) dbUpdates.releases = updates.releases;
  if (updates.claimInfo !== undefined) dbUpdates.claim_info = updates.claimInfo;
  if (updates.metaTitle !== undefined) dbUpdates.meta_title = updates.metaTitle;
  if (updates.metaDescription !== undefined) dbUpdates.meta_description = updates.metaDescription;
  if (updates.contentStatus !== undefined) dbUpdates.content_status = updates.contentStatus;
  if (updates.generatedContent !== undefined) dbUpdates.generated_content = updates.generatedContent;
  if (updates.publishedAt !== undefined) dbUpdates.published_at = updates.publishedAt;

  // Enhanced AI System fields
  if (updates.scrapedData !== undefined) dbUpdates.scraped_data = updates.scrapedData;
  if (updates.aiGenerationStatus !== undefined) dbUpdates.ai_generation_status = updates.aiGenerationStatus;
  if (updates.lastScrapedAt !== undefined) dbUpdates.last_scraped_at = updates.lastScrapedAt;
  if (updates.editorialReviewId !== undefined) dbUpdates.editorial_review_id = updates.editorialReviewId;
  if (updates.aiGenerationJobId !== undefined) dbUpdates.ai_generation_job_id = updates.aiGenerationJobId;
  if (updates.submissionType !== undefined) dbUpdates.submission_type = updates.submissionType;
  if (updates.submissionSource !== undefined) dbUpdates.submission_source = updates.submissionSource;
  if (updates.contentQualityScore !== undefined) dbUpdates.content_quality_score = updates.contentQualityScore;
  if (updates.lastAiUpdate !== undefined) dbUpdates.last_ai_update = updates.lastAiUpdate;

  // FAQ field
  if (updates.faqs !== undefined) dbUpdates.faqs = transformFAQArrayToDbFaqArray(updates.faqs);

  const { data, error } = await supabaseAdmin
    .from('tools')
    .update(dbUpdates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to update tool: ${error.message}`);
  }

  return transformDbToolToAITool(data);
}

export async function deleteTool(id: string): Promise<void> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  const { error } = await supabaseAdmin
    .from('tools')
    .delete()
    .eq('id', id);

  if (error) {
    throw new Error(`Failed to delete tool: ${error.message}`);
  }
}

// Search functionality
export async function searchTools(query: string, limit: number = 10): Promise<AITool[]> {
  const { data, error } = await supabase
    .from('tools')
    .select('*')
    .or(`name.ilike.%${query}%,description.ilike.%${query}%,features.cs.["${query}"]`)
    .eq('content_status', 'published')
    .limit(limit);

  if (error) {
    throw new Error(`Search failed: ${error.message}`);
  }

  return data?.map(transformDbToolToAITool) || [];
}

// Tool submission functions
export async function submitTool(submission: {
  name: string;
  url: string;
  description: string;
  category: string;
  subcategory?: string;
  submitterName?: string;
  submitterEmail: string;
  logoUrl?: string;
  tags?: string[];
  pricingType?: string;
}): Promise<void> {
  const { error } = await supabase
    .from('tool_submissions')
    .insert([{
      ...submission,
      status: 'pending',
      submitted_at: new Date().toISOString(),
    }]);

  if (error) {
    throw new Error(`Failed to submit tool: ${error.message}`);
  }
}

// Reviews functionality
export async function getToolReviews(toolId: string): Promise<any[]> {
  const { data, error } = await supabase
    .from('reviews')
    .select('*')
    .eq('tool_id', toolId)
    .eq('is_approved', true)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch reviews: ${error.message}`);
  }

  return data || [];
}

export async function submitReview(review: {
  toolId: string;
  userName: string;
  userEmail?: string;
  rating: number;
  title?: string;
  content: string;
}): Promise<void> {
  const { error } = await supabase
    .from('reviews')
    .insert([{
      tool_id: review.toolId,
      user_name: review.userName,
      user_email: review.userEmail,
      rating: review.rating,
      title: review.title,
      content: review.content,
      is_approved: false, // Requires moderation
      created_at: new Date().toISOString(),
    }]);

  if (error) {
    throw new Error(`Failed to submit review: ${error.message}`);
  }
}

// =====================================================
// FAQ CRUD OPERATIONS (JSONB-based)
// =====================================================

// Get FAQs for a specific tool
export async function getToolFAQs(toolId: string, filters: Partial<FAQFilters> = {}): Promise<FAQ[]> {
  const { data, error } = await supabase
    .from('tools')
    .select('faqs')
    .eq('id', toolId)
    .single();

  if (error) {
    throw new Error(`Failed to fetch tool FAQs: ${error.message}`);
  }

  if (!data?.faqs) {
    return [];
  }

  let faqs = transformDbFaqArrayToFAQArray(data.faqs);

  // Apply filters
  if (filters.category) {
    faqs = faqs.filter(faq => faq.category === filters.category);
  }

  if (filters.source) {
    faqs = faqs.filter(faq => faq.source === filters.source);
  }

  if (filters.isActive !== undefined) {
    faqs = faqs.filter(faq => faq.isActive === filters.isActive);
  }

  if (filters.isFeatured !== undefined) {
    faqs = faqs.filter(faq => faq.isFeatured === filters.isFeatured);
  }

  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    faqs = faqs.filter(faq =>
      faq.question.toLowerCase().includes(searchLower) ||
      faq.answer.toLowerCase().includes(searchLower)
    );
  }

  // Apply sorting
  const sortBy = filters.sortBy || 'displayOrder';
  const sortOrder = filters.sortOrder || 'asc';

  faqs.sort((a, b) => {
    let aVal: any, bVal: any;

    switch (sortBy) {
      case 'displayOrder':
        aVal = a.displayOrder || 0;
        bVal = b.displayOrder || 0;
        break;
      case 'priority':
        aVal = a.priority || 0;
        bVal = b.priority || 0;
        break;
      case 'helpScore':
        aVal = a.helpScore || 0;
        bVal = b.helpScore || 0;
        break;
      case 'viewCount':
        aVal = a.viewCount || 0;
        bVal = b.viewCount || 0;
        break;
      default:
        aVal = a.displayOrder || 0;
        bVal = b.displayOrder || 0;
    }

    if (sortOrder === 'desc') {
      return bVal - aVal;
    }
    return aVal - bVal;
  });

  // Apply limit
  if (filters.limit) {
    faqs = faqs.slice(0, filters.limit);
  }

  return faqs;
}

// Update tool FAQs (admin function)
export async function updateToolFAQs(toolId: string, faqs: FAQ[]): Promise<FAQ[]> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  const dbFaqArray = transformFAQArrayToDbFaqArray(faqs);

  const { data, error } = await supabaseAdmin
    .from('tools')
    .update({ faqs: dbFaqArray, updated_at: new Date().toISOString() })
    .eq('id', toolId)
    .select('faqs')
    .single();

  if (error) {
    throw new Error(`Failed to update tool FAQs: ${error.message}`);
  }

  return transformDbFaqArrayToFAQArray(data.faqs);
}

// Add FAQ to a tool
export async function addFAQToTool(toolId: string, faq: Partial<FAQ>): Promise<FAQ[]> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  // Get current FAQs
  const currentFaqs = await getToolFAQs(toolId);

  // Add new FAQ with generated ID
  const newFaq: FAQ = {
    id: crypto.randomUUID(),
    question: faq.question!,
    answer: faq.answer!,
    displayOrder: faq.displayOrder !== undefined ? faq.displayOrder : currentFaqs.length,
    priority: faq.priority || 0,
    category: faq.category || 'general',
    tags: faq.tags,
    isActive: faq.isActive !== undefined ? faq.isActive : true,
    isFeatured: faq.isFeatured || false,
    source: faq.source || 'manual',
    sourceMetadata: faq.sourceMetadata,
    metaKeywords: faq.metaKeywords,
    helpScore: faq.helpScore || 0,
    viewCount: faq.viewCount || 0,
  };

  const updatedFaqs = [...currentFaqs, newFaq];
  return await updateToolFAQs(toolId, updatedFaqs);
}

// Update specific FAQ in a tool
export async function updateFAQInTool(toolId: string, faqId: string, updates: Partial<FAQ>): Promise<FAQ[]> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  const currentFaqs = await getToolFAQs(toolId);
  const faqIndex = currentFaqs.findIndex(faq => faq.id === faqId);

  if (faqIndex === -1) {
    throw new Error('FAQ not found');
  }

  const updatedFaqs = [...currentFaqs];
  updatedFaqs[faqIndex] = { ...updatedFaqs[faqIndex], ...updates };

  return await updateToolFAQs(toolId, updatedFaqs);
}

// Remove FAQ from a tool
export async function removeFAQFromTool(toolId: string, faqId: string): Promise<FAQ[]> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  const currentFaqs = await getToolFAQs(toolId);
  const updatedFaqs = currentFaqs.filter(faq => faq.id !== faqId);

  return await updateToolFAQs(toolId, updatedFaqs);
}
