/**
 * Tool Version Comparison API Endpoint
 * Handles version comparison operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/lib/auth';
import { VersionComparator } from '@/lib/versioning/version-comparator';
import { CompareVersionsRequest } from '@/lib/types/versioning';

const versionComparator = new VersionComparator();

/**
 * GET /api/admin/tools/[id]/versions/compare
 * Compare two versions of a tool
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: toolId } = await params;
    const { searchParams } = new URL(request.url);

    const fromVersion = searchParams.get('fromVersion');
    const toVersion = searchParams.get('toVersion');
    const includeMetadata = searchParams.get('includeMetadata') === 'true';

    if (!fromVersion || !toVersion) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required parameters: fromVersion, toVersion' 
        },
        { status: 400 }
      );
    }

    const compareRequest: CompareVersionsRequest = {
      toolId,
      fromVersionNumber: parseInt(fromVersion),
      toVersionNumber: parseInt(toVersion),
      includeMetadata
    };

    const comparison = await versionComparator.compareVersions(compareRequest);

    // Generate human-readable summary
    const summary = versionComparator.generateChangeSummary(comparison);

    return NextResponse.json({
      success: true,
      data: {
        comparison,
        summary,
        metadata: {
          fromVersion: parseInt(fromVersion),
          toVersion: parseInt(toVersion),
          comparedAt: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Error comparing versions:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to compare versions' 
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/tools/[id]/versions/compare
 * Clean up expired comparison cache for a tool
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const cleanedCount = await versionComparator.cleanupExpiredComparisons();

    return NextResponse.json({
      success: true,
      data: {
        cleanedComparisons: cleanedCount,
        message: `Cleaned up ${cleanedCount} expired comparison${cleanedCount !== 1 ? 's' : ''}`
      }
    });

  } catch (error) {
    console.error('Error cleaning up comparisons:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to cleanup comparisons' 
      },
      { status: 500 }
    );
  }
}
