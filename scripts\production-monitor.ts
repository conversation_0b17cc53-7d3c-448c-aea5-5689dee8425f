#!/usr/bin/env tsx

/**
 * Production Monitoring Script
 * 
 * Continuous monitoring for production environment
 * - Health checks every 5 minutes
 * - Job queue monitoring
 * - Performance metrics
 * - Alert notifications
 */

import { createClient } from '@supabase/supabase-js';
import { getJobQueue } from '../src/lib/jobs';
import { JobStatus } from '../src/lib/jobs/types';
import nodemailer from 'nodemailer';

interface MonitoringMetrics {
  timestamp: Date;
  health: {
    database: boolean;
    jobQueue: boolean;
    openai: boolean;
    email: boolean;
  };
  performance: {
    dbResponseTime: number;
    queueResponseTime: number;
    totalJobs: number;
    failedJobs: number;
    pendingJobs: number;
  };
  alerts: Alert[];
}

interface Alert {
  level: 'info' | 'warning' | 'critical';
  message: string;
  timestamp: Date;
  details?: any;
}

class ProductionMonitor {
  private supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );
  private emailTransporter?: nodemailer.Transporter;
  private isRunning = false;
  private metrics: MonitoringMetrics[] = [];
  private lastAlertTime = new Map<string, Date>();

  constructor() {
    if (process.env.SMTP_HOST && process.env.SMTP_USER) {
      this.emailTransporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_PORT === '465',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });
    }
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('Monitor is already running');
      return;
    }

    this.isRunning = true;
    console.log('🔍 Starting Production Monitor...');
    console.log(`📊 Monitoring interval: 5 minutes`);
    console.log(`📧 Email alerts: ${this.emailTransporter ? 'enabled' : 'disabled'}`);
    console.log(`🎯 Admin email: ${process.env.ADMIN_EMAIL || 'not configured'}\n`);

    // Initial check
    await this.performHealthCheck();

    // Schedule regular checks every 5 minutes
    const interval = setInterval(async () => {
      if (!this.isRunning) {
        clearInterval(interval);
        return;
      }
      
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error('Health check failed:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down monitor...');
      this.isRunning = false;
      clearInterval(interval);
      process.exit(0);
    });

    // Keep the process running
    while (this.isRunning) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  private async performHealthCheck(): Promise<void> {
    const timestamp = new Date();
    const alerts: Alert[] = [];
    
    console.log(`\n🔍 Health Check - ${timestamp.toISOString()}`);

    // Database Health
    const dbStart = Date.now();
    let dbHealthy = false;
    try {
      const { error } = await this.supabase
        .from('categories')
        .select('count')
        .limit(1);
      
      dbHealthy = !error;
      if (error) {
        alerts.push({
          level: 'critical',
          message: `Database connection failed: ${error.message}`,
          timestamp,
          details: { error: error.message }
        });
      }
    } catch (error) {
      alerts.push({
        level: 'critical',
        message: `Database connection error: ${error}`,
        timestamp,
        details: { error }
      });
    }
    const dbResponseTime = Date.now() - dbStart;

    // Job Queue Health
    const queueStart = Date.now();
    let queueHealthy = false;
    let totalJobs = 0;
    let failedJobs = 0;
    let pendingJobs = 0;

    try {
      if (process.env.JOB_QUEUE_ENABLED === 'true') {
        const queue = getJobQueue();
        const jobs = await queue.getJobs();
        
        totalJobs = jobs.length;
        failedJobs = jobs.filter(job => job.status === JobStatus.FAILED).length;
        pendingJobs = jobs.filter(job => job.status === JobStatus.PENDING).length;
        
        queueHealthy = true;

        // Check for high failure rate
        if (totalJobs > 0 && failedJobs / totalJobs > 0.3) {
          alerts.push({
            level: 'critical',
            message: `High job failure rate: ${failedJobs}/${totalJobs} (${Math.round(failedJobs/totalJobs*100)}%)`,
            timestamp,
            details: { totalJobs, failedJobs, failureRate: failedJobs/totalJobs }
          });
        } else if (failedJobs > 20) {
          alerts.push({
            level: 'warning',
            message: `High number of failed jobs: ${failedJobs}`,
            timestamp,
            details: { failedJobs }
          });
        }

        // Check for job backlog
        if (pendingJobs > 100) {
          alerts.push({
            level: 'warning',
            message: `High job backlog: ${pendingJobs} pending jobs`,
            timestamp,
            details: { pendingJobs }
          });
        }
      } else {
        queueHealthy = true; // Queue is disabled, so it's "healthy"
      }
    } catch (error) {
      alerts.push({
        level: 'critical',
        message: `Job queue error: ${error}`,
        timestamp,
        details: { error }
      });
    }
    const queueResponseTime = Date.now() - queueStart;

    // OpenAI Health (basic check)
    let openaiHealthy = false;
    try {
      openaiHealthy = !!(process.env.OPENAI_API_KEY && 
                        process.env.OPENAI_API_KEY.startsWith('sk-'));
      
      if (!openaiHealthy) {
        alerts.push({
          level: 'warning',
          message: 'OpenAI API key not configured or invalid',
          timestamp,
        });
      }
    } catch (error) {
      alerts.push({
        level: 'warning',
        message: `OpenAI check failed: ${error}`,
        timestamp,
        details: { error }
      });
    }

    // Email Health
    let emailHealthy = false;
    try {
      emailHealthy = !!(process.env.SMTP_HOST && 
                       process.env.SMTP_USER && 
                       process.env.SMTP_PASS);
      
      if (!emailHealthy) {
        alerts.push({
          level: 'warning',
          message: 'Email service not configured',
          timestamp,
        });
      }
    } catch (error) {
      alerts.push({
        level: 'warning',
        message: `Email check failed: ${error}`,
        timestamp,
        details: { error }
      });
    }

    // Store metrics
    const metrics: MonitoringMetrics = {
      timestamp,
      health: {
        database: dbHealthy,
        jobQueue: queueHealthy,
        openai: openaiHealthy,
        email: emailHealthy,
      },
      performance: {
        dbResponseTime,
        queueResponseTime,
        totalJobs,
        failedJobs,
        pendingJobs,
      },
      alerts,
    };

    this.metrics.push(metrics);
    
    // Keep only last 24 hours of metrics (288 entries at 5-minute intervals)
    if (this.metrics.length > 288) {
      this.metrics = this.metrics.slice(-288);
    }

    // Log status
    this.logStatus(metrics);

    // Send alerts if necessary
    await this.processAlerts(alerts);
  }

  private logStatus(metrics: MonitoringMetrics): void {
    const { health, performance, alerts } = metrics;
    
    const dbIcon = health.database ? '✅' : '❌';
    const queueIcon = health.jobQueue ? '✅' : '❌';
    const openaiIcon = health.openai ? '✅' : '⚠️';
    const emailIcon = health.email ? '✅' : '⚠️';

    console.log(`${dbIcon} Database: ${performance.dbResponseTime}ms`);
    console.log(`${queueIcon} Job Queue: ${performance.queueResponseTime}ms (${performance.totalJobs} jobs, ${performance.failedJobs} failed, ${performance.pendingJobs} pending)`);
    console.log(`${openaiIcon} OpenAI: ${health.openai ? 'configured' : 'not configured'}`);
    console.log(`${emailIcon} Email: ${health.email ? 'configured' : 'not configured'}`);

    if (alerts.length > 0) {
      console.log(`\n🚨 Alerts (${alerts.length}):`);
      alerts.forEach(alert => {
        const icon = alert.level === 'critical' ? '🔴' : 
                     alert.level === 'warning' ? '🟡' : '🔵';
        console.log(`  ${icon} ${alert.level.toUpperCase()}: ${alert.message}`);
      });
    }
  }

  private async processAlerts(alerts: Alert[]): Promise<void> {
    const criticalAlerts = alerts.filter(alert => alert.level === 'critical');
    const warningAlerts = alerts.filter(alert => alert.level === 'warning');

    // Send email for critical alerts (with rate limiting)
    if (criticalAlerts.length > 0 && this.emailTransporter && process.env.ADMIN_EMAIL) {
      const alertKey = 'critical_alerts';
      const lastAlert = this.lastAlertTime.get(alertKey);
      const now = new Date();
      
      // Rate limit: only send critical alerts every 15 minutes
      if (!lastAlert || now.getTime() - lastAlert.getTime() > 15 * 60 * 1000) {
        await this.sendAlertEmail(criticalAlerts, 'critical');
        this.lastAlertTime.set(alertKey, now);
      }
    }

    // Send email for warning alerts (with rate limiting)
    if (warningAlerts.length > 0 && this.emailTransporter && process.env.ADMIN_EMAIL) {
      const alertKey = 'warning_alerts';
      const lastAlert = this.lastAlertTime.get(alertKey);
      const now = new Date();
      
      // Rate limit: only send warning alerts every 1 hour
      if (!lastAlert || now.getTime() - lastAlert.getTime() > 60 * 60 * 1000) {
        await this.sendAlertEmail(warningAlerts, 'warning');
        this.lastAlertTime.set(alertKey, now);
      }
    }
  }

  private async sendAlertEmail(alerts: Alert[], level: 'critical' | 'warning'): Promise<void> {
    if (!this.emailTransporter || !process.env.ADMIN_EMAIL) return;

    const subject = `🚨 AI Dude Directory - ${level.toUpperCase()} Alert`;
    const alertList = alerts.map(alert => 
      `• ${alert.message} (${alert.timestamp.toISOString()})`
    ).join('\n');

    const html = `
      <h2>🚨 ${level.toUpperCase()} Alert - AI Dude Directory</h2>
      <p><strong>Time:</strong> ${new Date().toISOString()}</p>
      <p><strong>Environment:</strong> ${process.env.NODE_ENV || 'development'}</p>
      
      <h3>Alerts (${alerts.length}):</h3>
      <ul>
        ${alerts.map(alert => `<li><strong>${alert.level.toUpperCase()}:</strong> ${alert.message}</li>`).join('')}
      </ul>
      
      <h3>Recent Metrics:</h3>
      ${this.generateMetricsHTML()}
      
      <p><em>This is an automated alert from the AI Dude Directory monitoring system.</em></p>
    `;

    try {
      await this.emailTransporter.sendMail({
        from: `"AI Dude Directory Monitor" <${process.env.SMTP_USER}>`,
        to: process.env.ADMIN_EMAIL,
        subject,
        html,
        text: `${subject}\n\nAlerts:\n${alertList}`,
      });
      
      console.log(`📧 Alert email sent for ${alerts.length} ${level} alerts`);
    } catch (error) {
      console.error('Failed to send alert email:', error);
    }
  }

  private generateMetricsHTML(): string {
    if (this.metrics.length === 0) return '<p>No metrics available</p>';

    const latest = this.metrics[this.metrics.length - 1];
    return `
      <table border="1" style="border-collapse: collapse;">
        <tr>
          <th>Component</th>
          <th>Status</th>
          <th>Response Time</th>
          <th>Details</th>
        </tr>
        <tr>
          <td>Database</td>
          <td>${latest.health.database ? '✅ Healthy' : '❌ Error'}</td>
          <td>${latest.performance.dbResponseTime}ms</td>
          <td>-</td>
        </tr>
        <tr>
          <td>Job Queue</td>
          <td>${latest.health.jobQueue ? '✅ Healthy' : '❌ Error'}</td>
          <td>${latest.performance.queueResponseTime}ms</td>
          <td>${latest.performance.totalJobs} total, ${latest.performance.failedJobs} failed</td>
        </tr>
        <tr>
          <td>OpenAI</td>
          <td>${latest.health.openai ? '✅ Configured' : '⚠️ Not Configured'}</td>
          <td>-</td>
          <td>Content Generation: ${process.env.CONTENT_GENERATION_ENABLED === 'true' ? 'Enabled' : 'Disabled'}</td>
        </tr>
        <tr>
          <td>Email</td>
          <td>${latest.health.email ? '✅ Configured' : '⚠️ Not Configured'}</td>
          <td>-</td>
          <td>SMTP: ${process.env.SMTP_HOST || 'Not configured'}</td>
        </tr>
      </table>
    `;
  }

  stop(): void {
    this.isRunning = false;
  }
}

// Run monitor if called directly
if (require.main === module) {
  const monitor = new ProductionMonitor();
  monitor.start().catch(error => {
    console.error('Monitor failed:', error);
    process.exit(1);
  });
}

export { ProductionMonitor };
