#!/usr/bin/env tsx

/**
 * Apply Content Status Migration - Extend content_status field
 * 
 * This script applies the migration to extend the content_status field
 * to support editorial workflow states: under_review, approved, rejected
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyContentStatusMigration() {
  console.log('🚀 Applying Content Status Migration...');
  console.log('============================================================');

  try {
    // Check if migration has already been applied
    console.log('📋 Checking if migration already completed...');

    const { data: migrationCheck } = await supabase
      .from('system_configuration')
      .select('config_value')
      .eq('config_key', 'migration_003_content_status_completed')
      .single();

    if (migrationCheck) {
      console.log('✅ Migration 003 has already been completed. Skipping...');
      return;
    }

    console.log('🔧 Applying content status constraint update...');

    // Execute the migration steps manually
    console.log('   1. Dropping existing constraint...');
    const { error: dropError } = await supabase
      .rpc('exec_sql', {
        sql: 'ALTER TABLE tools DROP CONSTRAINT IF EXISTS tools_content_status_check;'
      });

    if (dropError) {
      console.log(`   ⚠️  Drop constraint error: ${dropError.message}`);
    } else {
      console.log('   ✅ Existing constraint dropped');
    }

    console.log('   2. Adding new constraint with extended values...');
    const { error: addError } = await supabase
      .rpc('exec_sql', {
        sql: `ALTER TABLE tools ADD CONSTRAINT tools_content_status_check
              CHECK (content_status IN ('draft', 'published', 'archived', 'under_review', 'approved', 'rejected'));`
      });

    if (addError) {
      console.log(`   ❌ Add constraint error: ${addError.message}`);
      throw addError;
    } else {
      console.log('   ✅ New constraint added successfully');
    }

    // Verify the migration by testing the new constraint
    console.log('🔍 Verifying migration...');
    
    // Test that new status values are accepted
    const testStatuses = ['under_review', 'approved', 'rejected'];
    for (const status of testStatuses) {
      const { error: testError } = await supabase
        .from('tools')
        .select('id')
        .eq('content_status', status)
        .limit(1);
        
      if (testError) {
        console.log(`   ❌ Status '${status}' not supported: ${testError.message}`);
      } else {
        console.log(`   ✅ Status '${status}' is supported`);
      }
    }

    // Mark migration as completed
    console.log('📝 Marking migration as completed...');
    const { error: markError } = await supabase
      .from('system_configuration')
      .upsert({
        config_key: 'migration_003_content_status_completed',
        config_value: { 
          completed_at: new Date().toISOString(), 
          version: '1.0.0',
          extended_statuses: ['under_review', 'approved', 'rejected']
        },
        config_type: 'system',
        description: 'Content status extension migration completion marker'
      }, { onConflict: 'config_key' });

    if (markError) {
      console.log('⚠️  Could not mark migration as completed:', markError.message);
    } else {
      console.log('✅ Migration marked as completed');
    }

    console.log('');
    console.log('🎉 Content Status Migration completed successfully!');
    console.log('   The content_status field now supports:');
    console.log('   • draft');
    console.log('   • published');
    console.log('   • archived');
    console.log('   • under_review');
    console.log('   • approved');
    console.log('   • rejected');
    console.log('');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Execute the migration
if (require.main === module) {
  applyContentStatusMigration().catch(console.error);
}
