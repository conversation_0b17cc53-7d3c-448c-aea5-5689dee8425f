/**
 * Tool Version Rollback API Endpoint
 * Handles rollback operations for tool versions
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/lib/auth';
import { VersionManager } from '@/lib/versioning/version-manager';
import { RollbackRequest } from '@/lib/types/versioning';

const versionManager = new VersionManager();

/**
 * POST /api/admin/tools/[id]/versions/rollback
 * Rollback a tool to a specific version
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: toolId } = await params;
    const body = await request.json();

    // Validate required fields
    if (!body.targetVersionNumber || !body.reason || !body.performedBy) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: targetVersionNumber, reason, performedBy' 
        },
        { status: 400 }
      );
    }

    // Extract user context from headers
    const userAgent = request.headers.get('user-agent') || undefined;
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const ipAddress = forwardedFor?.split(',')[0] || realIp || undefined;

    const rollbackRequest: RollbackRequest = {
      toolId,
      targetVersionNumber: parseInt(body.targetVersionNumber),
      reason: body.reason,
      performedBy: body.performedBy,
      sessionId: body.sessionId,
      requestId: body.requestId || `rollback_${Date.now()}`,
      ipAddress,
      userAgent
    };

    // Validate rollback before proceeding
    const validation = await versionManager.validateRollback(rollbackRequest);
    
    if (!validation.canRollback) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Rollback validation failed',
          details: {
            errors: validation.errors,
            warnings: validation.warnings
          }
        },
        { status: 400 }
      );
    }

    // Perform rollback
    const result = await versionManager.rollbackToVersion(rollbackRequest);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      operationId: result.operationId,
      warnings: validation.warnings.length > 0 ? validation.warnings : undefined
    });

  } catch (error) {
    console.error('Error performing rollback:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to perform rollback' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/tools/[id]/versions/rollback/validate
 * Validate if rollback is possible for a specific version
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: toolId } = await params;
    const { searchParams } = new URL(request.url);
    const targetVersionNumber = searchParams.get('targetVersion');

    if (!targetVersionNumber) {
      return NextResponse.json(
        { success: false, error: 'Missing targetVersion parameter' },
        { status: 400 }
      );
    }

    const rollbackRequest: RollbackRequest = {
      toolId,
      targetVersionNumber: parseInt(targetVersionNumber),
      reason: 'Validation check',
      performedBy: 'system'
    };

    const validation = await versionManager.validateRollback(rollbackRequest);

    return NextResponse.json({
      success: true,
      data: validation
    });

  } catch (error) {
    console.error('Error validating rollback:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to validate rollback' 
      },
      { status: 500 }
    );
  }
}
