'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AddToolForm } from '@/components/admin/AddToolForm';

export default function NewToolPage() {
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);

  const handleSuccess = (toolId: string) => {
    setIsRedirecting(true);
    // Redirect to the tool edit page or back to admin tools list
    router.push(`/admin/tools/${toolId}/edit`);
  };

  const handleCancel = () => {
    router.push('/admin');
  };

  if (isRedirecting) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Redirecting to tool editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-zinc-900 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-gray-400 mb-4">
            <span>Admin</span>
            <span>›</span>
            <span>Tools</span>
            <span>›</span>
            <span className="text-white">Add New Tool</span>
          </div>
          
          <h1 className="text-3xl font-bold text-white mb-2">
            Add New AI Tool
          </h1>
          <p className="text-gray-300">
            Create a new AI tool entry for the directory. Fill in the required information and optionally add detailed content.
          </p>
        </div>

        {/* Add Tool Form */}
        <AddToolForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />

        {/* Help Section */}
        <div className="mt-8 bg-zinc-800 border border-zinc-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">💡 Tips for Adding Tools</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-300 mb-2">Required Information</h4>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Tool name and URL are required</li>
                <li>• Description should be clear and informative</li>
                <li>• Select the most appropriate category</li>
                <li>• Tools are saved as drafts by default</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-300 mb-2">Best Practices</h4>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Use high-quality logo images (PNG/SVG preferred)</li>
                <li>• Write detailed descriptions for better SEO</li>
                <li>• Add features to help users understand capabilities</li>
                <li>• Verify tools before publishing</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
