#!/usr/bin/env tsx

/**
 * Test pg_trgm Extension Migration
 * 
 * This script tests the pg_trgm extension enablement and similarity function
 * to ensure the migration will work correctly in the Supabase environment.
 */

import { createClient } from '@supabase/supabase-js';

async function testPgTrgmMigration() {
  console.log('🧪 Testing pg_trgm Extension Migration...');
  console.log('=' .repeat(60));

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.log('❌ Missing Supabase environment variables');
    console.log('   Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // Test 1: Check if pg_trgm extension can be enabled
    console.log('\n📋 Test 1: Checking pg_trgm extension availability...');
    
    const { data: extensionData, error: extensionError } = await supabase
      .rpc('exec_sql', {
        sql: 'CREATE EXTENSION IF NOT EXISTS pg_trgm;'
      });

    if (extensionError) {
      console.log(`❌ Extension enablement failed: ${extensionError.message}`);
      console.log('   This might indicate insufficient permissions or extension unavailability');
      return;
    } else {
      console.log('✅ pg_trgm extension enabled successfully');
    }

    // Test 2: Test similarity function
    console.log('\n📋 Test 2: Testing similarity() function...');
    
    const { data: similarityData, error: similarityError } = await supabase
      .rpc('exec_sql', {
        sql: "SELECT similarity('ChatGPT', 'Chat GPT') as test_similarity;"
      });

    if (similarityError) {
      console.log(`❌ Similarity function test failed: ${similarityError.message}`);
      return;
    } else {
      console.log('✅ similarity() function working correctly');
      console.log(`   Test result: similarity('ChatGPT', 'Chat GPT') = ${similarityData?.[0]?.test_similarity || 'N/A'}`);
    }

    // Test 3: Test the view creation (simplified version)
    console.log('\n📋 Test 3: Testing view creation with similarity...');
    
    const viewTestSql = `
      SELECT 
        'test1' as tool1_name,
        'test2' as tool2_name,
        similarity(LOWER('ChatGPT'), LOWER('Chat GPT')) as name_similarity
      LIMIT 1;
    `;

    const { data: viewData, error: viewError } = await supabase
      .rpc('exec_sql', {
        sql: viewTestSql
      });

    if (viewError) {
      console.log(`❌ View test failed: ${viewError.message}`);
      return;
    } else {
      console.log('✅ View creation with similarity function will work');
      console.log(`   Sample similarity score: ${viewData?.[0]?.name_similarity || 'N/A'}`);
    }

    // Test 4: Check existing tools table structure
    console.log('\n📋 Test 4: Checking tools table structure...');
    
    const { data: toolsData, error: toolsError } = await supabase
      .from('tools')
      .select('id, name, slug')
      .limit(1);

    if (toolsError) {
      console.log(`❌ Tools table access failed: ${toolsError.message}`);
      return;
    } else {
      console.log('✅ Tools table accessible');
      console.log(`   Sample tool count check: ${toolsData?.length || 0} tools found`);
    }

    console.log('\n🎉 ✅ ALL TESTS PASSED!');
    console.log('   The pg_trgm migration should work correctly.');
    console.log('   You can now safely run the migration.');

  } catch (error: any) {
    console.log(`❌ Unexpected error: ${error.message}`);
    console.log('   Please check your database connection and permissions.');
  }

  console.log('');
}

// Run the test
testPgTrgmMigration().catch(console.error);
