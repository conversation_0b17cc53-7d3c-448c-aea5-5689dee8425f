'use client';

import React, { useState, useEffect } from 'react';
import { DbCategory } from '@/lib/types';
import { apiClient } from '@/lib/api';
import { CategoryForm } from './CategoryForm';
import { CategoryTable } from './CategoryTable';

export function CategoryManager() {
  const [categories, setCategories] = useState<DbCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<DbCategory | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    setLoading(true);
    setError(null);

    try {
      const adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'admin-dashboard-access';
      const data = await apiClient.getAdminCategories(adminApiKey);
      setCategories(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNew = () => {
    setEditingCategory(null);
    setShowForm(true);
  };

  const handleEdit = (category: DbCategory) => {
    setEditingCategory(category);
    setShowForm(true);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingCategory(null);
    loadCategories();
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingCategory(null);
  };

  const filteredCategories = categories.filter(category =>
    category.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-900 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="text-xl">Loading categories...</div>
          </div>
        </div>
      </div>
    );
  }

  if (showForm) {
    return (
      <div className="min-h-screen bg-zinc-900 text-white">
        <div className="container mx-auto px-4 py-8">
          <CategoryForm
            category={editingCategory || undefined}
            mode={editingCategory ? 'edit' : 'create'}
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold font-roboto">Category Management</h1>
            <p className="text-gray-400 mt-2">
              Manage AI tool categories and their properties
            </p>
          </div>
          <div className="flex space-x-3">
            <a
              href="/admin"
              className="bg-zinc-700 hover:bg-zinc-600 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              ← Back to Admin
            </a>
            <button
              onClick={handleCreateNew}
              className="bg-orange-500 hover:bg-orange-600 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              + Create Category
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            <strong>Error:</strong> {error}
            <button
              onClick={loadCategories}
              className="ml-4 underline hover:no-underline"
            >
              Retry
            </button>
          </div>
        )}

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
            <h3 className="text-lg font-semibold mb-2">Total Categories</h3>
            <p className="text-3xl font-bold text-orange-500">{categories.length}</p>
          </div>
          <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
            <h3 className="text-lg font-semibold mb-2">With Icons</h3>
            <p className="text-3xl font-bold text-blue-500">
              {categories.filter(c => c.icon_name).length}
            </p>
          </div>
          <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
            <h3 className="text-lg font-semibold mb-2">With Descriptions</h3>
            <p className="text-3xl font-bold text-green-500">
              {categories.filter(c => c.description).length}
            </p>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-3 bg-zinc-800 border border-zinc-700 text-white rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent pl-10"
            />
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              🔍
            </div>
          </div>
        </div>

        {/* Categories Table */}
        <CategoryTable
          categories={filteredCategories}
          onEdit={handleEdit}
          onRefresh={loadCategories}
        />

        {filteredCategories.length === 0 && searchTerm && (
          <div className="text-center py-8">
            <p className="text-gray-400">No categories found matching "{searchTerm}"</p>
            <button
              onClick={() => setSearchTerm('')}
              className="text-orange-500 hover:text-orange-400 underline mt-2"
            >
              Clear search
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
