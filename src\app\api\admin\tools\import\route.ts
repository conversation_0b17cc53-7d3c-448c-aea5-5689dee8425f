import { NextRequest, NextResponse } from 'next/server';
import { createTool, getAdminTools } from '../../../../../lib/supabase';
import { validateApiKey } from '../../../../../lib/auth';
import { AITool } from '../../../../../lib/types';

interface ImportError {
  row: number;
  error: string;
  data?: Record<string, unknown>;
}

interface ImportDuplicate {
  row: number;
  name: string;
  slug: string;
}

interface ImportResult {
  success: boolean;
  imported: number;
  skipped: number;
  errors: ImportError[];
  duplicates: ImportDuplicate[];
}

/**
 * POST /api/admin/tools/import
 * Import tool data from CSV or JSON format
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const preview = formData.get('preview') === 'true';
    const duplicateStrategy = formData.get('duplicateStrategy') || 'skip'; // skip, update, error

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!['csv', 'json'].includes(fileExtension || '')) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only CSV and JSON files are supported' },
        { status: 400 }
      );
    }

    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, error: 'File size exceeds 50MB limit' },
        { status: 400 }
      );
    }

    // Process file based on type
    let importData: Record<string, unknown>[];
    const fileContent = await file.text();

    if (fileExtension === 'json') {
      importData = await processJSONImport(fileContent);
    } else {
      importData = await processCSVImport(fileContent);
    }

    // If preview mode, return processed data without importing
    if (preview) {
      return NextResponse.json({
        success: true,
        preview: true,
        totalRows: importData.length,
        sampleData: importData.slice(0, 10), // Return first 10 rows for preview
        fields: importData.length > 0 ? Object.keys(importData[0]) : []
      });
    }

    // Perform actual import
    const result = await performImport(importData, duplicateStrategy as string);

    return NextResponse.json({
      success: true,
      result
    });

  } catch (error) {
    console.error('Tool import error:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Failed to import tools' },
      { status: 500 }
    );
  }
}

/**
 * Process JSON import file
 */
async function processJSONImport(content: string): Promise<Record<string, unknown>[]> {
  try {
    const data = JSON.parse(content);
    
    // Handle different JSON formats
    if (Array.isArray(data)) {
      return data;
    } else if (data.tools && Array.isArray(data.tools)) {
      return data.tools;
    } else if (data.data && Array.isArray(data.data)) {
      return data.data;
    } else {
      throw new Error('Invalid JSON format. Expected array or object with "tools" or "data" property');
    }
  } catch (error) {
    throw new Error(`Invalid JSON format: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Process CSV import file
 */
async function processCSVImport(content: string): Promise<Record<string, unknown>[]> {
  const lines = content.split('\n').filter(line => line.trim());
  
  if (lines.length < 2) {
    throw new Error('CSV file must contain at least a header row and one data row');
  }

  // Parse header row
  const headers = parseCSVRow(lines[0]);
  
  // Parse data rows
  const data: Record<string, unknown>[] = [];
  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVRow(lines[i]);
    if (values.length === 0) continue; // Skip empty rows

    const row: Record<string, unknown> = {};
    headers.forEach((header, index) => {
      const value = values[index] || '';
      
      // Convert header to snake_case field name
      const fieldName = header.toLowerCase()
        .replace(/\s+/g, '_')
        .replace(/[^a-z0-9_]/g, '');
      
      // Parse special fields
      if (fieldName.includes('json') || fieldName === 'features' || fieldName === 'pricing') {
        try {
          row[fieldName] = value ? JSON.parse(value) : null;
        } catch {
          row[fieldName] = value;
        }
      } else if (fieldName.includes('is_') || fieldName.includes('verified') || fieldName.includes('claimed')) {
        row[fieldName] = value.toLowerCase() === 'true' || value === '1';
      } else {
        row[fieldName] = value;
      }
    });
    
    data.push(row);
  }

  return data;
}

/**
 * Parse a single CSV row, handling quoted values
 */
function parseCSVRow(row: string): string[] {
  const result: string[] = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < row.length; i++) {
    const char = row[i];
    
    if (char === '"') {
      if (inQuotes && row[i + 1] === '"') {
        // Escaped quote
        current += '"';
        i++; // Skip next quote
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      result.push(current.trim());
      current = '';
    } else {
      current += char;
    }
  }
  
  // Add final field
  result.push(current.trim());
  
  return result;
}

/**
 * Perform the actual import operation
 */
async function performImport(importData: Record<string, unknown>[], duplicateStrategy: string): Promise<ImportResult> {
  const result: ImportResult = {
    success: true,
    imported: 0,
    skipped: 0,
    errors: [],
    duplicates: []
  };

  // Get existing tools to check for duplicates
  const { data: existingTools } = await getAdminTools();
  const existingSlugs = new Set(existingTools.map(tool => tool.slug));
  const existingNames = new Set(existingTools.map(tool => tool.name.toLowerCase()));

  for (let i = 0; i < importData.length; i++) {
    const rowData = importData[i];
    
    try {
      // Validate required fields
      if (!rowData.name || !rowData.link) {
        result.errors.push({
          row: i + 1,
          error: 'Missing required fields: name and link are required',
          data: rowData
        });
        continue;
      }

      // Generate slug if not provided
      const slug = (rowData.slug as string) || generateSlug(rowData.name as string);

      // Check for duplicates
      const isDuplicateName = existingNames.has((rowData.name as string).toLowerCase());
      const isDuplicateSlug = existingSlugs.has(slug);
      
      if (isDuplicateName || isDuplicateSlug) {
        result.duplicates.push({
          row: i + 1,
          name: rowData.name as string,
          slug: slug
        });
        
        if (duplicateStrategy === 'skip') {
          result.skipped++;
          continue;
        } else if (duplicateStrategy === 'error') {
          result.errors.push({
            row: i + 1,
            error: `Duplicate tool: ${rowData.name} (slug: ${slug})`,
            data: rowData
          });
          continue;
        }
        // For 'update' strategy, we would need to implement update logic
      }

      // Create tool data
      const toolData: Partial<AITool> = {
        name: rowData.name as string,
        slug: slug,
        description: (rowData.description as string) || '',
        shortDescription: (rowData.short_description as string) || '',
        detailedDescription: (rowData.detailed_description as string) || '',
        link: rowData.link as string,
        website: (rowData.website as string) || '',
        category: (rowData.category_id as string) || 'ai-tools',
        subcategory: (rowData.subcategory as string) || '',
        company: (rowData.company as string) || '',
        isVerified: Boolean(rowData.is_verified) || false,
        isClaimed: Boolean(rowData.is_claimed) || false,
        contentStatus: (rowData.content_status as any) || 'draft',
        features: (rowData.features as string[]) || null,
        pricing: (rowData.pricing as any) || null,
        logoUrl: (rowData.logo_url as string) || '',
        submissionType: 'admin',
        submissionSource: 'import'
      };

      // Create the tool
      await createTool(toolData);
      
      // Add to existing sets to prevent duplicates within the same import
      existingSlugs.add(slug);
      existingNames.add((rowData.name as string).toLowerCase());
      
      result.imported++;
      
    } catch (error) {
      result.errors.push({
        row: i + 1,
        error: error instanceof Error ? error.message : 'Unknown error',
        data: rowData
      });
    }
  }

  return result;
}

/**
 * Generate URL-friendly slug from name
 */
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '') // Remove leading/trailing dashes
    .trim();
}
