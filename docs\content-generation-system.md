# Content Generation System

## Overview

The Content Generation system provides a comprehensive AI-powered content creation and management workflow for the AI tools directory. It includes content generation, review processes, queue management, and prompt optimization.

## System Components

### 1. Main Dashboard (`/admin/content`)
- **Overview**: Central hub for content generation activities
- **Features**:
  - Real-time statistics (generated content, queue size, failed generation)
  - Quick action buttons to access sub-systems
  - Generation queue preview
  - Start/stop content generation controls

### 2. AI Configuration (`/admin/content/ai-config`)
- **Purpose**: Manage AI providers and generation settings
- **Features**:
  - Multiple AI provider support (OpenAI, OpenRouter, Anthropic)
  - API key management with secure display
  - Model selection and configuration
  - Temperature and token limit controls
  - Provider testing and status monitoring
  - Default provider selection

### 3. Generation Queue (`/admin/content/queue`)
- **Purpose**: Monitor and manage content generation jobs
- **Features**:
  - Real-time job status tracking
  - Progress monitoring with visual indicators
  - Priority management (high, medium, low)
  - Bulk operations (retry, pause, resume, cancel)
  - Search and filtering capabilities
  - Estimated completion times
  - Error handling and retry mechanisms

### 4. Content Review (`/admin/content/review`)
- **Purpose**: Editorial workflow for generated content
- **Features**:
  - Content quality scoring
  - Review status management (pending, under review, approved, rejected)
  - Content preview with full-screen modal
  - Issue tracking and notes
  - Approval/rejection workflow
  - Editorial comments and feedback

### 5. Prompt Management (`/admin/content/prompts`)
- **Purpose**: Optimize and manage AI generation prompts
- **Features**:
  - Template library with categories
  - Variable substitution system
  - Prompt testing and optimization
  - Usage analytics
  - Template versioning
  - Copy and export functionality

## Technical Architecture

### Backend Components
- **API Routes**: `/api/generate-content/route.ts` - Main content generation endpoint
- **Pipeline**: `src/lib/content-generation/pipeline.ts` - Orchestrates the generation workflow
- **AI Generator**: `src/lib/ai/content-generator.ts` - Handles AI provider interactions
- **Validation**: `src/lib/content-generation/validator.ts` - Content quality validation
- **Editorial Controls**: `src/lib/content-generation/editorial-controls.ts` - Review workflow

### Frontend Components
- **Pages**: Located in `src/app/admin/content/`
- **Shared UI**: Uses common components from `src/components/ui/`
- **State Management**: React hooks with local state
- **Real-time Updates**: Simulated with periodic refreshes (can be enhanced with WebSockets)

## Content Generation Workflow

### 1. Initiation
- User triggers content generation from dashboard
- System adds job to generation queue
- Priority and options are set

### 2. Processing
- AI provider is selected based on configuration
- Content is generated using optimized prompts
- Quality validation is performed
- Results are stored with metadata

### 3. Review
- Generated content enters review queue
- Editorial team reviews and scores content
- Approval/rejection decisions are made
- Feedback is provided for improvements

### 4. Publication
- Approved content is marked for publication
- Content is integrated into the main system
- Analytics and usage tracking begins

## Configuration

### AI Providers
The system supports multiple AI providers:

1. **OpenAI**
   - Model: `gpt-4o-2024-11-20`
   - Best for: High-quality content generation
   - Cost: Higher but reliable

2. **OpenRouter**
   - Model: `google/gemini-2.0-flash-exp:free`
   - Best for: Cost-effective generation
   - Cost: Lower, good for bulk operations

3. **Anthropic**
   - Model: `claude-3-sonnet-20240229`
   - Best for: Detailed analysis and reviews
   - Cost: Moderate, high quality

### Prompt Templates
The system includes several prompt categories:

- **Content**: Main tool descriptions and reviews
- **Features**: Bullet-point feature extraction
- **Pros/Cons**: Balanced analysis lists
- **Description**: Short summaries for listings
- **Pricing**: Pricing information extraction

## Getting Started

### 1. Configure AI Providers
1. Navigate to `/admin/content/ai-config`
2. Add API keys for your preferred providers
3. Test connectivity using the "Test" button
4. Set a default provider for automatic generation

### 2. Customize Prompts
1. Go to `/admin/content/prompts`
2. Review existing templates
3. Modify prompts to match your content style
4. Test prompts with sample data

### 3. Start Generation
1. Return to `/admin/content`
2. Click "Start Generation" to begin processing
3. Monitor progress in the queue
4. Review generated content as it becomes available

### 4. Review and Approve
1. Check `/admin/content/review` for pending content
2. Preview generated content
3. Approve or reject based on quality
4. Provide feedback for improvements

## Monitoring and Analytics

### Queue Metrics
- Total jobs in queue
- Processing status distribution
- Average completion times
- Error rates and retry counts

### Content Quality
- Quality scores by provider
- Approval/rejection rates
- Common issues and feedback
- Content length and completeness

### System Performance
- API response times
- Provider availability
- Cost tracking per provider
- Usage patterns and trends

## Troubleshooting

### Common Issues

1. **API Key Errors**
   - Verify API keys are correct
   - Check provider account status
   - Ensure sufficient credits/quota

2. **Generation Failures**
   - Review error messages in queue
   - Check prompt template validity
   - Verify content input quality

3. **Quality Issues**
   - Adjust prompt templates
   - Modify AI provider settings
   - Review temperature and token limits

### Support Resources
- Check system logs for detailed errors
- Use the test functionality to isolate issues
- Review provider documentation for API changes
- Monitor system health in the admin dashboard

## Future Enhancements

### Planned Features
- Real-time WebSocket updates
- Advanced analytics dashboard
- Automated quality scoring
- Bulk content operations
- Integration with external tools
- Custom AI model fine-tuning

### Integration Opportunities
- CMS integration for direct publishing
- SEO optimization tools
- Social media scheduling
- Translation services
- Image generation integration

## Security Considerations

### API Key Management
- Keys are masked in the UI
- Secure storage in environment variables
- Regular rotation recommended
- Access logging and monitoring

### Content Validation
- Input sanitization
- Output content filtering
- Malicious content detection
- Rate limiting and abuse prevention

### Access Control
- Admin-only access to configuration
- Role-based permissions for review
- Audit logging for all actions
- Secure API endpoints with authentication
