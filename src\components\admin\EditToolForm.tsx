'use client';

import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ChevronDown, GitBranch } from 'lucide-react';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/Button';
import { AICategory, AITool } from '@/lib/types';
import { transformFormDataToAITool, transformAIToolToFormData, validateFieldMappings } from '@/lib/data-transformers';
import { VersionManagement } from './versioning/VersionManagement';

// Validation schema for Edit Tool Form (same as Add Tool Form)
const editToolSchema = z.object({
  // Basic Information
  name: z.string().min(1, 'Tool name is required').max(255, 'Tool name must be less than 255 characters'),
  slug: z.string().optional(), // Auto-generated from name
  description: z.string().min(10, 'Description must be at least 10 characters').max(500, 'Description must be less than 500 characters'),
  short_description: z.string().max(150, 'Short description must be less than 150 characters').optional().or(z.literal('')),
  detailed_description: z.string().max(2000, 'Detailed description must be less than 2000 characters').optional().or(z.literal('')),

  // URLs
  link: z.string().optional().or(z.literal('')), // URL slug like /tools/chatgpt
  website: z.string().url('Please enter a valid website URL for the tool'),
  logo_url: z.string().url('Please enter a valid logo URL').optional().or(z.literal('')),

  // Categorization
  category_id: z.string().min(1, 'Please select a category'),
  subcategory: z.string().optional(),

  // Company Information
  company: z.string().max(255, 'Company name must be less than 255 characters').optional(),

  // Features (JSON array)
  features: z.string().optional(), // Will be parsed as JSON array

  // Phase 1: Critical Fields (JSONB)
  screenshots: z.array(z.string().url('Please enter a valid screenshot URL')).max(10, 'Maximum 10 screenshots allowed').optional(),
  pricing: z.object({
    type: z.enum(['free', 'freemium', 'paid', 'open source', 'subscription']),
    plans: z.array(z.object({
      name: z.string().min(1, 'Plan name is required'),
      price: z.string().min(1, 'Price is required'),
      features: z.array(z.string()).optional()
    })).optional()
  }).optional(),

  // Phase 2: Important Fields (JSONB)
  social_links: z.object({
    twitter: z.string().url('Please enter a valid Twitter URL').optional().or(z.literal('')),
    linkedin: z.string().url('Please enter a valid LinkedIn URL').optional().or(z.literal('')),
    github: z.string().url('Please enter a valid GitHub URL').optional().or(z.literal('')),
    website: z.string().url('Please enter a valid website URL').optional().or(z.literal(''))
  }).optional(),
  pros_and_cons: z.object({
    pros: z.array(z.string().min(1, 'Pro cannot be empty')).max(10, 'Maximum 10 pros allowed').optional(),
    cons: z.array(z.string().min(1, 'Con cannot be empty')).max(10, 'Maximum 10 cons allowed').optional()
  }).optional(),

  // Phase 3: Enhancement Fields
  hashtags: z.array(z.string().min(1, 'Hashtag cannot be empty')).max(20, 'Maximum 20 hashtags allowed').optional(),
  content_quality_score: z.number().min(1, 'Score must be at least 1').max(100, 'Score must be at most 100').optional(),

  // Phase 4: Advanced Fields (JSONB)
  releases: z.array(z.object({
    version: z.string().min(1, 'Version is required'),
    date: z.string().min(1, 'Date is required'),
    notes: z.string().min(1, 'Release notes are required'),
    isLatest: z.boolean().optional()
  })).optional(),
  faqs: z.array(z.object({
    question: z.string().min(5, 'Question must be at least 5 characters').max(500, 'Question must be less than 500 characters'),
    answer: z.string().min(10, 'Answer must be at least 10 characters').max(5000, 'Answer must be less than 5000 characters'),
    category: z.enum(['general', 'pricing', 'features', 'support', 'getting-started']).optional(),
    displayOrder: z.number().min(0, 'Display order must be 0 or greater').optional(),
    priority: z.number().min(0, 'Priority must be 0 or greater').max(10, 'Priority must be 10 or less').optional(),
    isActive: z.boolean().optional(),
    isFeatured: z.boolean().optional()
  })).optional(),

  // Metadata
  meta_title: z.string().max(255, 'Meta title must be less than 255 characters').optional(),
  meta_description: z.string().max(500, 'Meta description must be less than 500 characters').optional(),

  // Status and Workflow
  content_status: z.enum(['draft', 'published', 'archived', 'under_review', 'approved', 'rejected']),
  submission_type: z.enum(['admin', 'user_url', 'user_full']),
  submission_source: z.string(),
  ai_generation_status: z.enum(['pending', 'processing', 'completed', 'failed', 'skipped']),

  // Verification
  is_verified: z.boolean(),
  is_claimed: z.boolean(),
});

type EditToolFormData = z.infer<typeof editToolSchema>;

interface EditToolFormProps {
  tool: AITool;
  onSuccess?: (toolId: string) => void;
  onCancel?: () => void;
}

export function EditToolForm({ tool, onSuccess, onCancel }: EditToolFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [categories, setCategories] = useState<AICategory[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [showVersioning, setShowVersioning] = useState(false);
  const [createVersion, setCreateVersion] = useState(true);
  const [expandedSections, setExpandedSections] = useState({
    basic: true,
    media: false,
    pricing: false,
    social: false,
    content: false,
    advanced: false,
    releases: false,
    faqs: false,
    metadata: false,
    status: false
  });

  // Helper function to safely parse JSON fields
  const safeJsonParse = (jsonString: any) => {
    if (!jsonString) return undefined;
    if (typeof jsonString === 'object') return jsonString; // Already parsed
    try {
      return JSON.parse(jsonString);
    } catch {
      return undefined;
    }
  };

  // Transform AITool data to form data
  // Transform AITool data to form data using centralized transformer
  const transformToolToFormData = (aiTool: AITool): Partial<EditToolFormData> => {
    return transformAIToolToFormData(aiTool);
  };

  const {
    register,
    handleSubmit,
    formState: { errors, isValid, isSubmitting: formIsSubmitting },
    reset,
    watch,
    setValue,
    control
  } = useForm<EditToolFormData>({
    resolver: zodResolver(editToolSchema),
    mode: 'onChange',
    defaultValues: transformToolToFormData(tool)
  });

  // Field arrays for dynamic inputs
  const {
    fields: screenshotFields,
    append: appendScreenshot,
    remove: removeScreenshot
  } = useFieldArray({
    control,
    name: 'screenshots' as any
  });

  const {
    fields: hashtagFields,
    append: appendHashtag,
    remove: removeHashtag
  } = useFieldArray({
    control,
    name: 'hashtags' as any
  });

  const {
    fields: prosFields,
    append: appendPro,
    remove: removePro
  } = useFieldArray({
    control,
    name: 'pros_and_cons.pros' as any
  });

  const {
    fields: consFields,
    append: appendCon,
    remove: removeCon
  } = useFieldArray({
    control,
    name: 'pros_and_cons.cons' as any
  });

  const {
    fields: releaseFields,
    append: appendRelease,
    remove: removeRelease
  } = useFieldArray({
    control,
    name: 'releases' as any
  });

  const {
    fields: pricingPlanFields,
    append: appendPricingPlan,
    remove: removePricingPlan
  } = useFieldArray({
    control,
    name: 'pricing.plans' as any
  });

  const {
    fields: faqFields,
    append: appendFAQ,
    remove: removeFAQ
  } = useFieldArray({
    control,
    name: 'faqs' as any
  });

  // Watch pricing type to show/hide plans
  const pricingType = watch('pricing.type');

  // Watch name field to auto-generate slug
  const watchedName = watch('name');

  // Helper functions for section management
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const addScreenshot = () => {
    appendScreenshot('' as any);
  };

  const addHashtag = () => {
    appendHashtag('' as any);
  };

  const addPro = () => {
    appendPro('' as any);
  };

  const addCon = () => {
    appendCon('' as any);
  };

  const addRelease = () => {
    appendRelease({
      version: '',
      date: '',
      notes: '',
      isLatest: false
    } as any);
  };

  const addPricingPlan = () => {
    appendPricingPlan({
      name: '',
      price: '',
      features: []
    } as any);
  };

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await apiClient.getCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Failed to load categories:', error);
      } finally {
        setLoadingCategories(false);
      }
    };

    loadCategories();
  }, []);

  // Reset form values after categories are loaded to ensure proper initialization
  useEffect(() => {
    if (!loadingCategories && categories.length > 0) {
      const formData = transformToolToFormData(tool);
      reset(formData);
    }
  }, [loadingCategories, categories, tool, reset]);

  // Auto-generate slug from name
  useEffect(() => {
    if (watchedName) {
      const slug = watchedName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      setValue('slug', slug);
    }
  }, [watchedName, setValue]);

  const onSubmit = async (data: any) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Parse features field
      let parsedFeatures: string[] | null = null;
      if (data.features && data.features.trim()) {
        try {
          // Try to parse as JSON first
          parsedFeatures = JSON.parse(data.features);
        } catch {
          // If not JSON, split by lines and filter empty lines
          parsedFeatures = data.features
            .split('\n')
            .map((line: string) => line.trim())
            .filter((line: string) => line.length > 0);
        }
      }

      // Ensure link field is populated (required by database)
      // If no URL slug provided, generate one from the tool name/slug
      const linkValue = data.link && data.link.trim() ? data.link : `/tools/${data.slug || data.name?.toLowerCase().replace(/\s+/g, '-')}`;

      // Prepare data with parsed features and link
      const dataWithParsedFeatures = {
        ...data,
        link: linkValue,
        features: parsedFeatures
      };

      // Transform form data to AITool interface using centralized transformer
      const processedData = transformFormDataToAITool(dataWithParsedFeatures);

      // Validate field mappings to ensure no data loss
      const validation = validateFieldMappings(dataWithParsedFeatures, processedData);
      if (!validation.isValid) {
        console.warn('Field mapping validation failed:', validation.missingFields);
        setSubmitError(`Data validation failed: Missing fields ${validation.missingFields.join(', ')}`);
        return;
      }

      // Prepare tool data for API
      const toolData = processedData;

      // Get admin API key from environment or session
      const adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';

      // Update tool via admin API
      const updatedTool = await apiClient.updateAdminTool(tool.id, toolData, adminApiKey, createVersion);
      
      setSuccess(true);

      if (onSuccess && updatedTool.id) {
        setTimeout(() => onSuccess(updatedTool.id), 2000);
      }
    } catch (err) {
      setSubmitError(err instanceof Error ? err.message : 'Failed to update tool');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
        <div className="text-center">
          <div className="text-green-400 text-6xl mb-4">✓</div>
          <h2 className="text-xl font-semibold text-white mb-2">Tool Updated Successfully!</h2>
          <p className="text-gray-300 mb-6">
            The tool has been updated and saved. Redirecting back to admin panel...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white">Edit AI Tool</h2>
        {onCancel && (
          <Button onClick={onCancel} variant="outline" size="sm">
            Cancel
          </Button>
        )}
      </div>

      <form onSubmit={(e) => {
        console.log('🎯 Edit form onSubmit event triggered!', e);
        return handleSubmit(onSubmit)(e);
      }} className="space-y-6">
        {submitError && (
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
            <p className="text-red-400 text-sm">{submitError}</p>
          </div>
        )}



        {/* Basic Information Section */}
        <div className="border border-zinc-600 rounded-lg">
          <button
            type="button"
            onClick={() => toggleSection('basic')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-zinc-700/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">Basic Information</h3>
            <span className="text-gray-400">
              {expandedSections.basic ? '−' : '+'}
            </span>
          </button>

          {expandedSections.basic && (
            <div className="p-4 border-t border-zinc-600 space-y-4">
              {/* Tool Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                  Tool Name *
                </label>
                <input
                  type="text"
                  id="name"
                  {...register('name')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.name ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Enter the name of the AI tool"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-400">{errors.name.message}</p>
                )}
              </div>

              {/* Tool URL (Main Website) */}
              <div>
                <label htmlFor="website" className="block text-sm font-medium text-gray-300 mb-2">
                  Tool URL *
                </label>
                <div className="space-y-2">
                  <input
                    type="url"
                    id="website"
                    {...register('website')}
                    className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                      errors.website ? 'border-red-500' : 'border-zinc-600'
                    }`}
                    placeholder="https://tool-website.com"
                  />
                  {/* Visit Tool Button */}
                  {watch('website') && (
                    <button
                      type="button"
                      onClick={() => {
                        const websiteUrl = watch('website');
                        if (websiteUrl) {
                          window.open(websiteUrl, '_blank', 'noopener,noreferrer');
                        }
                      }}
                      className="w-full bg-orange-500 hover:bg-orange-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                      Visit Tool
                    </button>
                  )}
                </div>
                {errors.website && (
                  <p className="mt-1 text-sm text-red-400">{errors.website.message}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-2">
                  Description *
                </label>
                <textarea
                  id="description"
                  rows={3}
                  {...register('description')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical ${
                    errors.description ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Brief description of the AI tool (10-500 characters)"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-400">{errors.description.message}</p>
                )}
              </div>

              {/* Category */}
              <div>
                <label htmlFor="category_id" className="block text-sm font-medium text-gray-300 mb-2">
                  Category *
                </label>
                <select
                  id="category_id"
                  {...register('category_id')}
                  disabled={loadingCategories}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.category_id ? 'border-red-500' : 'border-zinc-600'
                  }`}
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.title}
                    </option>
                  ))}
                </select>
                {errors.category_id && (
                  <p className="mt-1 text-sm text-red-400">{errors.category_id.message}</p>
                )}
              </div>

              {/* Company */}
              <div>
                <label htmlFor="company" className="block text-sm font-medium text-gray-300 mb-2">
                  Company
                </label>
                <input
                  type="text"
                  id="company"
                  {...register('company')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.company ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Company or organization name"
                />
                {errors.company && (
                  <p className="mt-1 text-sm text-red-400">{errors.company.message}</p>
                )}
              </div>

              {/* URL Slug (Auto-generated) */}
              <div>
                <label htmlFor="link" className="block text-sm font-medium text-gray-300 mb-2">
                  URL Slug <span className="text-gray-500">(auto-generated)</span>
                </label>
                <input
                  type="text"
                  id="link"
                  {...register('link')}
                  readOnly
                  className="w-full px-3 py-2 bg-zinc-800 border border-zinc-600 rounded-md text-gray-400 cursor-not-allowed"
                  placeholder="/tools/chatgpt (internal tool page URL)"
                />
                <p className="mt-1 text-xs text-gray-400">
                  URL slugs are automatically generated from the tool name and cannot be manually edited.
                </p>
              </div>

              {/* Logo URL */}
              <div>
                <label htmlFor="logo_url" className="block text-sm font-medium text-gray-300 mb-2">
                  Logo URL
                </label>
                <input
                  type="url"
                  id="logo_url"
                  {...register('logo_url')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.logo_url ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="https://example.com/logo.png"
                />
                {errors.logo_url && (
                  <p className="mt-1 text-sm text-red-400">{errors.logo_url.message}</p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Media & Screenshots Section */}
        <div className="border border-zinc-600 rounded-lg">
          <button
            type="button"
            onClick={() => toggleSection('media')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-zinc-700/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">Media & Screenshots</h3>
            <span className="text-gray-400">
              {expandedSections.media ? '−' : '+'}
            </span>
          </button>

          {expandedSections.media && (
            <div className="p-4 border-t border-zinc-600 space-y-4">
              {/* Screenshots */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-300">
                    Screenshots
                  </label>
                  <Button
                    onClick={addScreenshot}
                    variant="outline"
                    size="sm"
                    disabled={screenshotFields.length >= 10}
                  >
                    Add Screenshot
                  </Button>
                </div>

                {screenshotFields.length > 0 && (
                  <div className="space-y-2">
                    {screenshotFields.map((field, index) => (
                      <div key={field.id} className="flex items-center space-x-2">
                        <input
                          type="url"
                          {...register(`screenshots.${index}` as const)}
                          className={`flex-1 px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                            errors.screenshots?.[index] ? 'border-red-500' : 'border-zinc-600'
                          }`}
                          placeholder={`Screenshot ${index + 1} URL`}
                        />
                        <Button
                          onClick={() => removeScreenshot(index)}
                          variant="outline"
                          size="sm"
                          className="text-red-400 hover:text-red-300"
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                {screenshotFields.length === 0 && (
                  <p className="text-sm text-gray-400">No screenshots added yet. Click "Add Screenshot" to add one.</p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Pricing Section */}
        <div className="border border-zinc-600 rounded-lg">
          <button
            type="button"
            onClick={() => toggleSection('pricing')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-zinc-700/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">Pricing Information</h3>
            <span className="text-gray-400">
              {expandedSections.pricing ? '−' : '+'}
            </span>
          </button>

          {expandedSections.pricing && (
            <div className="p-4 border-t border-zinc-600 space-y-4">
              {/* Pricing Type */}
              <div>
                <label htmlFor="pricing_type" className="block text-sm font-medium text-gray-300 mb-2">
                  Pricing Type
                </label>
                <select
                  id="pricing_type"
                  {...register('pricing.type')}
                  className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value="">Select pricing type</option>
                  <option value="free">Free</option>
                  <option value="freemium">Freemium</option>
                  <option value="paid">Paid</option>
                  <option value="subscription">Subscription</option>
                  <option value="open source">Open Source</option>
                </select>
              </div>

              {/* Pricing Plans */}
              {pricingType && pricingType !== 'free' && pricingType !== 'open source' && (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Pricing Plans
                    </label>
                    <Button
                      onClick={addPricingPlan}
                      variant="outline"
                      size="sm"
                    >
                      Add Plan
                    </Button>
                  </div>

                  {pricingPlanFields.length > 0 && (
                    <div className="space-y-3">
                      {pricingPlanFields.map((field, index) => (
                        <div key={field.id} className="border border-zinc-600 rounded-md p-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-300">Plan {index + 1}</span>
                            <Button
                              onClick={() => removePricingPlan(index)}
                              variant="outline"
                              size="sm"
                              className="text-red-400 hover:text-red-300"
                            >
                              Remove
                            </Button>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <input
                              type="text"
                              {...register(`pricing.plans.${index}.name` as const)}
                              className="px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                              placeholder="Plan name"
                            />
                            <input
                              type="text"
                              {...register(`pricing.plans.${index}.price` as const)}
                              className="px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                              placeholder="Price (e.g., $9/month)"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Content Section */}
        <div className="border border-zinc-600 rounded-lg">
          <button
            type="button"
            onClick={() => toggleSection('content')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-zinc-700/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">Content & Features</h3>
            <span className="text-gray-400">
              {expandedSections.content ? '−' : '+'}
            </span>
          </button>

          {expandedSections.content && (
            <div className="p-4 border-t border-zinc-600 space-y-4">
              {/* Short Description */}
              <div>
                <label htmlFor="short_description" className="block text-sm font-medium text-gray-300 mb-2">
                  Short Description
                </label>
                <textarea
                  id="short_description"
                  rows={2}
                  {...register('short_description')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical ${
                    errors.short_description ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Brief one-liner description (max 150 characters)"
                />
                {errors.short_description && (
                  <p className="mt-1 text-sm text-red-400">{errors.short_description.message}</p>
                )}
              </div>

              {/* Detailed Description */}
              <div>
                <label htmlFor="detailed_description" className="block text-sm font-medium text-gray-300 mb-2">
                  Detailed Description
                </label>
                <textarea
                  id="detailed_description"
                  rows={4}
                  {...register('detailed_description')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical ${
                    errors.detailed_description ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Comprehensive description of the tool (50-2000 characters)"
                />
                {errors.detailed_description && (
                  <p className="mt-1 text-sm text-red-400">{errors.detailed_description.message}</p>
                )}
              </div>

              {/* Features */}
              <div>
                <label htmlFor="features" className="block text-sm font-medium text-gray-300 mb-2">
                  Features
                </label>
                <textarea
                  id="features"
                  rows={4}
                  {...register('features')}
                  className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical"
                  placeholder="Enter features one per line or as JSON array:&#10;• AI-powered content generation&#10;• Real-time collaboration&#10;• Multi-language support"
                />
                <p className="mt-1 text-xs text-gray-400">
                  Enter features one per line or as a JSON array (e.g., ["Feature 1", "Feature 2"])
                </p>
              </div>

              {/* Pros and Cons */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Pros */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Pros
                    </label>
                    <Button
                      onClick={addPro}
                      variant="outline"
                      size="sm"
                      disabled={prosFields.length >= 10}
                    >
                      Add Pro
                    </Button>
                  </div>

                  {prosFields.length > 0 && (
                    <div className="space-y-2">
                      {prosFields.map((field, index) => (
                        <div key={field.id} className="flex items-center space-x-2">
                          <input
                            type="text"
                            {...register(`pros_and_cons.pros.${index}` as const)}
                            className={`flex-1 px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                              errors.pros_and_cons?.pros?.[index] ? 'border-red-500' : 'border-zinc-600'
                            }`}
                            placeholder={`Pro ${index + 1}`}
                          />
                          <Button
                            onClick={() => removePro(index)}
                            variant="outline"
                            size="sm"
                            className="text-red-400 hover:text-red-300"
                          >
                            ×
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Cons */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Cons
                    </label>
                    <Button
                      onClick={addCon}
                      variant="outline"
                      size="sm"
                      disabled={consFields.length >= 10}
                    >
                      Add Con
                    </Button>
                  </div>

                  {consFields.length > 0 && (
                    <div className="space-y-2">
                      {consFields.map((field, index) => (
                        <div key={field.id} className="flex items-center space-x-2">
                          <input
                            type="text"
                            {...register(`pros_and_cons.cons.${index}` as const)}
                            className={`flex-1 px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                              errors.pros_and_cons?.cons?.[index] ? 'border-red-500' : 'border-zinc-600'
                            }`}
                            placeholder={`Con ${index + 1}`}
                          />
                          <Button
                            onClick={() => removeCon(index)}
                            variant="outline"
                            size="sm"
                            className="text-red-400 hover:text-red-300"
                          >
                            ×
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Hashtags */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-300">
                    Hashtags
                  </label>
                  <Button
                    onClick={addHashtag}
                    variant="outline"
                    size="sm"
                    disabled={hashtagFields.length >= 20}
                  >
                    Add Hashtag
                  </Button>
                </div>

                {hashtagFields.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {hashtagFields.map((field, index) => (
                      <div key={field.id} className="flex items-center space-x-2">
                        <input
                          type="text"
                          {...register(`hashtags.${index}` as const)}
                          className={`flex-1 px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                            errors.hashtags?.[index] ? 'border-red-500' : 'border-zinc-600'
                          }`}
                          placeholder={`#tag${index + 1}`}
                        />
                        <Button
                          onClick={() => removeHashtag(index)}
                          variant="outline"
                          size="sm"
                          className="text-red-400 hover:text-red-300"
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* FAQs Section */}
        <div className="border border-zinc-600 rounded-lg">
          <button
            type="button"
            onClick={() => toggleSection('faqs')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-zinc-700/50 transition-colors"
          >
            <div>
              <h3 className="text-lg font-medium text-white">FAQs</h3>
              <p className="text-sm text-gray-400">Frequently asked questions</p>
            </div>
            <ChevronDown
              className={`w-5 h-5 text-gray-400 transition-transform ${expandedSections.faqs ? 'rotate-180' : ''}`}
            />
          </button>

          {expandedSections.faqs && (
            <div className="p-4 border-t border-zinc-600 space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-400">
                  Add frequently asked questions to help users understand the tool better.
                </p>
                <button
                  type="button"
                  onClick={() => appendFAQ({
                    question: '',
                    answer: '',
                    category: 'general',
                    displayOrder: faqFields.length,
                    priority: 0,
                    isActive: true,
                    isFeatured: false
                  })}
                  className="px-3 py-1 bg-orange-500 hover:bg-orange-600 text-white text-sm rounded-md"
                >
                  Add FAQ
                </button>
              </div>

              {faqFields.length > 0 && (
                <div className="space-y-4">
                  {faqFields.map((field, index) => (
                    <div key={field.id} className="p-4 bg-zinc-800 rounded-lg border border-zinc-700 space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-white">FAQ #{index + 1}</h4>
                        <button
                          type="button"
                          onClick={() => removeFAQ(index)}
                          className="text-red-400 hover:text-red-300 text-sm"
                        >
                          Remove
                        </button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-400 mb-1">
                            Category
                          </label>
                          <select
                            {...register(`faqs.${index}.category` as const)}
                            className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                          >
                            <option value="general">General</option>
                            <option value="pricing">Pricing</option>
                            <option value="features">Features</option>
                            <option value="support">Support</option>
                            <option value="getting-started">Getting Started</option>
                          </select>
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <label className="block text-xs font-medium text-gray-400 mb-1">
                              Display Order
                            </label>
                            <input
                              type="number"
                              min="0"
                              {...register(`faqs.${index}.displayOrder` as const, { valueAsNumber: true })}
                              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                              placeholder="0"
                            />
                          </div>

                          <div>
                            <label className="block text-xs font-medium text-gray-400 mb-1">
                              Priority (0-10)
                            </label>
                            <input
                              type="number"
                              min="0"
                              max="10"
                              {...register(`faqs.${index}.priority` as const, { valueAsNumber: true })}
                              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                              placeholder="0"
                            />
                          </div>
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-400 mb-1">
                          Question *
                        </label>
                        <input
                          type="text"
                          {...register(`faqs.${index}.question` as const)}
                          className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                          placeholder="What is this tool used for?"
                        />
                        {errors.faqs?.[index]?.question && (
                          <p className="mt-1 text-xs text-red-400">{errors.faqs[index]?.question?.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-400 mb-1">
                          Answer *
                        </label>
                        <textarea
                          rows={4}
                          {...register(`faqs.${index}.answer` as const)}
                          className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm resize-vertical"
                          placeholder="This tool is designed to help users..."
                        />
                        {errors.faqs?.[index]?.answer && (
                          <p className="mt-1 text-xs text-red-400">{errors.faqs[index]?.answer?.message}</p>
                        )}
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id={`faq_${index}_active`}
                            {...register(`faqs.${index}.isActive` as const)}
                            className="w-4 h-4 text-orange-500 bg-zinc-700 border-zinc-600 rounded focus:ring-orange-500 focus:ring-2"
                          />
                          <label htmlFor={`faq_${index}_active`} className="ml-2 text-xs text-gray-300">
                            Active
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id={`faq_${index}_featured`}
                            {...register(`faqs.${index}.isFeatured` as const)}
                            className="w-4 h-4 text-orange-500 bg-zinc-700 border-zinc-600 rounded focus:ring-orange-500 focus:ring-2"
                          />
                          <label htmlFor={`faq_${index}_featured`} className="ml-2 text-xs text-gray-300">
                            Featured
                          </label>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {faqFields.length === 0 && (
                <p className="text-sm text-gray-400 italic">
                  No FAQs added. Click "Add FAQ" to help users understand the tool better.
                </p>
              )}

              <p className="mt-1 text-xs text-gray-400">
                FAQs help users quickly find answers to common questions about the tool.
              </p>
            </div>
          )}
        </div>

        {/* Releases & Updates Section */}
        <div className="border border-zinc-600 rounded-lg">
          <button
            type="button"
            onClick={() => toggleSection('releases')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-zinc-700/50 transition-colors"
          >
            <div>
              <h3 className="text-lg font-medium text-white">Releases & Updates</h3>
              <p className="text-sm text-gray-400">Track version releases and updates</p>
            </div>
            <ChevronDown
              className={`w-5 h-5 text-gray-400 transition-transform ${expandedSections.releases ? 'rotate-180' : ''}`}
            />
          </button>

          {expandedSections.releases && (
            <div className="p-4 border-t border-zinc-600 space-y-4">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-300">
                  Releases
                </label>
                <Button
                  onClick={addRelease}
                  variant="outline"
                  size="sm"
                >
                  Add Release
                </Button>
              </div>

              {releaseFields.length > 0 && (
                <div className="space-y-4">
                  {releaseFields.map((field, index) => (
                    <div key={field.id} className="bg-zinc-700/50 p-4 rounded-lg border border-zinc-600">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-sm font-medium text-white">Release {index + 1}</h4>
                        <Button
                          onClick={() => removeRelease(index)}
                          variant="outline"
                          size="sm"
                          className="text-red-400 hover:text-red-300"
                        >
                          Remove
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-400 mb-1">
                            Version
                          </label>
                          <input
                            type="text"
                            {...register(`releases.${index}.version` as const)}
                            className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                            placeholder="e.g., v1.2.0"
                          />
                          {errors.releases?.[index]?.version && (
                            <p className="mt-1 text-xs text-red-400">{errors.releases[index]?.version?.message}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-xs font-medium text-gray-400 mb-1">
                            Date
                          </label>
                          <input
                            type="date"
                            {...register(`releases.${index}.date` as const)}
                            className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                          />
                          {errors.releases?.[index]?.date && (
                            <p className="mt-1 text-xs text-red-400">{errors.releases[index]?.date?.message}</p>
                          )}
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id={`release_${index}_latest`}
                            {...register(`releases.${index}.isLatest` as const)}
                            className="w-4 h-4 text-orange-500 bg-zinc-700 border-zinc-600 rounded focus:ring-orange-500 focus:ring-2"
                          />
                          <label htmlFor={`release_${index}_latest`} className="ml-2 text-xs text-gray-300">
                            Latest Release
                          </label>
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-400 mb-1">
                          Release Notes
                        </label>
                        <textarea
                          rows={3}
                          {...register(`releases.${index}.notes` as const)}
                          className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm resize-vertical"
                          placeholder="What's new in this release..."
                        />
                        {errors.releases?.[index]?.notes && (
                          <p className="mt-1 text-xs text-red-400">{errors.releases[index]?.notes?.message}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {releaseFields.length === 0 && (
                <p className="text-sm text-gray-400 italic">
                  No releases added. Click "Add Release" to track version history.
                </p>
              )}

              <p className="mt-1 text-xs text-gray-400">
                Track version releases and updates for this tool.
              </p>
            </div>
          )}
        </div>

        {/* Status & Settings Section */}
        <div className="border border-zinc-600 rounded-lg">
          <button
            type="button"
            onClick={() => toggleSection('status')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-zinc-700/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">Status & Settings</h3>
            <span className="text-gray-400">
              {expandedSections.status ? '−' : '+'}
            </span>
          </button>

          {expandedSections.status && (
            <div className="p-4 border-t border-zinc-600 space-y-4">
              {/* Content Status */}
              <div>
                <label htmlFor="content_status" className="block text-sm font-medium text-gray-300 mb-2">
                  Content Status
                </label>
                <select
                  id="content_status"
                  {...register('content_status')}
                  className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              {/* AI Generation Status */}
              <div>
                <label htmlFor="ai_generation_status" className="block text-sm font-medium text-gray-300 mb-2">
                  AI Generation Status
                </label>
                <select
                  id="ai_generation_status"
                  {...register('ai_generation_status')}
                  className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                  <option value="skipped">Skipped</option>
                </select>
              </div>

              {/* Content Quality Score */}
              <div>
                <label htmlFor="content_quality_score" className="block text-sm font-medium text-gray-300 mb-2">
                  Content Quality Score (1-100)
                </label>
                <input
                  type="number"
                  id="content_quality_score"
                  min="1"
                  max="100"
                  {...register('content_quality_score', { valueAsNumber: true })}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.content_quality_score ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Enter quality score (1-100)"
                />
                {errors.content_quality_score && (
                  <p className="mt-1 text-sm text-red-400">{errors.content_quality_score.message}</p>
                )}
              </div>

              {/* Verification Status */}
              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('is_verified')}
                    className="mr-2 rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500 focus:ring-offset-zinc-800"
                  />
                  <span className="text-sm text-gray-300">Verified Tool</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('is_claimed')}
                    className="mr-2 rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500 focus:ring-offset-zinc-800"
                  />
                  <span className="text-sm text-gray-300">Claimed Tool</span>
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Version Management Section */}
        <div className="border border-zinc-600 rounded-lg">
          <button
            type="button"
            onClick={() => setShowVersioning(!showVersioning)}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-zinc-700/50 transition-colors"
          >
            <div className="flex items-center gap-3">
              <GitBranch className="w-5 h-5 text-blue-400" />
              <div>
                <h3 className="text-lg font-medium text-white">Version Management</h3>
                <p className="text-sm text-gray-400">View version history and manage rollbacks</p>
              </div>
            </div>
            <ChevronDown
              className={`w-5 h-5 text-gray-400 transition-transform ${showVersioning ? 'rotate-180' : ''}`}
            />
          </button>

          {showVersioning && (
            <div className="border-t border-zinc-600">
              <VersionManagement
                toolId={tool.id}
                toolName={tool.name}
              />
            </div>
          )}
        </div>

        {/* Version Creation Option */}
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-blue-300">Version Control</h4>
              <p className="text-xs text-blue-400 mt-1">
                Create a new version when saving changes to track modifications
              </p>
            </div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={createVersion}
                onChange={(e) => setCreateVersion(e.target.checked)}
                className="mr-2 rounded border-blue-500 bg-zinc-700 text-blue-500 focus:ring-blue-500 focus:ring-offset-zinc-800"
              />
              <span className="text-sm text-blue-300">Create version on save</span>
            </label>
          </div>
        </div>

        {/* Form submission buttons */}
        <div className="flex items-center space-x-3">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              disabled={isSubmitting}
              className="px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-md border border-zinc-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            disabled={isSubmitting}
            className="min-w-[120px] px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Updating...</span>
              </div>
            ) : (
              'Update Tool'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
