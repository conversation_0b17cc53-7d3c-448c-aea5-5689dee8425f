import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/admin/analytics/export
 * Export analytics data as CSV
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '7d';

    // Get tools data
    const { data: toolsData, error: toolsError } = await supabase
      .from('tools')
      .select('id, name, category, status, created_at, updated_at')
      .order('created_at', { ascending: false });

    if (toolsError) {
      console.error('Error fetching tools data:', toolsError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch tools data' },
        { status: 500 }
      );
    }

    // Generate CSV content
    const csvHeaders = [
      'Tool ID',
      'Tool Name', 
      'Category',
      'Status',
      'Created Date',
      'Updated Date',
      'Mock Views',
      'Mock Rating'
    ];

    const csvRows = toolsData.map(tool => [
      tool.id,
      `"${tool.name.replace(/"/g, '""')}"`, // Escape quotes in CSV
      tool.category || 'AI Tools',
      tool.status,
      tool.created_at,
      tool.updated_at,
      Math.floor(Math.random() * 2000) + 500, // Mock views
      (4.0 + Math.random() * 0.8).toFixed(1) // Mock rating
    ]);

    // Combine headers and rows
    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.join(','))
    ].join('\n');

    // Return CSV response
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="analytics-${range}-${new Date().toISOString().split('T')[0]}.csv"`
      }
    });

  } catch (error) {
    console.error('Analytics export API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
