'use client';

import React, { useState } from 'react';
import { DbCategory } from '@/lib/types';
import { apiClient } from '@/lib/api';

interface CategoryTableProps {
  categories: DbCategory[];
  onEdit: (category: DbCategory) => void;
  onRefresh: () => void;
}

export function CategoryTable({ categories, onEdit, onRefresh }: CategoryTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  const handleDelete = async (category: DbCategory) => {
    if (!confirm(`Are you sure you want to delete the category "${category.title}"? This action cannot be undone.`)) {
      return;
    }

    setDeletingId(category.id);
    setDeleteError(null);

    try {
      const adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'admin-dashboard-access';
      await apiClient.deleteAdminCategory(category.id, adminApiKey);
      onRefresh();
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : 'Failed to delete category');
    } finally {
      setDeletingId(null);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (categories.length === 0) {
    return (
      <div className="bg-zinc-800 p-8 rounded-lg border border-zinc-700 text-center">
        <div className="text-gray-400 text-lg mb-4">No categories found</div>
        <p className="text-gray-500">Create your first category to get started.</p>
      </div>
    );
  }

  return (
    <div className="bg-zinc-800 rounded-lg border border-zinc-700 overflow-hidden">
      {deleteError && (
        <div className="bg-red-900/50 border-b border-red-500 text-red-200 px-4 py-3">
          <strong>Error:</strong> {deleteError}
        </div>
      )}
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-zinc-800 border-b border-black">
            <tr>
              <th className="text-left py-3 px-4 font-medium text-white">Title</th>
              <th className="text-left py-3 px-4 font-medium text-white">Icon</th>
              <th className="text-left py-3 px-4 font-medium text-white">Description</th>
              <th className="text-left py-3 px-4 font-medium text-white">Colors</th>
              <th className="text-left py-3 px-4 font-medium text-white">Created</th>
              <th className="text-left py-3 px-4 font-medium text-white">Actions</th>
            </tr>
          </thead>
          <tbody>
            {categories.map((category) => (
              <tr
                key={category.id}
                className="border-b border-zinc-700 hover:bg-zinc-800 transition-colors"
              >
                <td className="py-3 px-4">
                  <div>
                    <div className="font-medium text-white">{category.title}</div>
                    <div className="text-sm text-gray-400">ID: {category.id}</div>
                  </div>
                </td>
                <td className="py-3 px-4">
                  <span className="text-gray-300 font-mono text-sm">
                    {category.icon_name || 'N/A'}
                  </span>
                </td>
                <td className="py-3 px-4">
                  <div className="text-gray-300 text-sm max-w-xs truncate">
                    {category.description || 'No description'}
                  </div>
                </td>
                <td className="py-3 px-4">
                  <div className="space-y-1">
                    {category.color_class && (
                      <div className="text-xs font-mono text-gray-400">
                        BG: {category.color_class}
                      </div>
                    )}
                    {category.text_color_class && (
                      <div className="text-xs font-mono text-gray-400">
                        Text: {category.text_color_class}
                      </div>
                    )}
                  </div>
                </td>
                <td className="py-3 px-4">
                  <span className="text-gray-300 text-sm">
                    {formatDate(category.created_at)}
                  </span>
                </td>
                <td className="py-3 px-4">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => onEdit(category)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete(category)}
                      disabled={deletingId === category.id}
                      className="bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                    >
                      {deletingId === category.id ? 'Deleting...' : 'Delete'}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
