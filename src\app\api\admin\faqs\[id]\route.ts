import { NextRequest, NextResponse } from 'next/server';
import { updateFAQInTool, removeFAQFromTool } from '@/lib/supabase';
import { validateApiKey } from '@/lib/auth';
import { FAQ } from '@/lib/types';

/**
 * This endpoint now requires toolId in the request body since FAQs are stored within tools
 * The [id] parameter represents the FAQ ID within the tool's FAQ array
 */

/**
 * PUT /api/admin/faqs/[id]
 * Update a specific FAQ within a tool (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'FAQ ID is required' },
        { status: 400 }
      );
    }

    const { toolId, updates }: { toolId: string; updates: Partial<FAQ> } = await request.json();

    if (!toolId) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    // Validate question length if provided
    if (updates.question !== undefined) {
      if (!updates.question.trim()) {
        return NextResponse.json(
          { success: false, error: 'Question cannot be empty' },
          { status: 400 }
        );
      }
      if (updates.question.length < 5 || updates.question.length > 500) {
        return NextResponse.json(
          { success: false, error: 'Question must be between 5 and 500 characters' },
          { status: 400 }
        );
      }
    }

    // Validate answer length if provided
    if (updates.answer !== undefined) {
      if (!updates.answer.trim()) {
        return NextResponse.json(
          { success: false, error: 'Answer cannot be empty' },
          { status: 400 }
        );
      }
      if (updates.answer.length < 10 || updates.answer.length > 5000) {
        return NextResponse.json(
          { success: false, error: 'Answer must be between 10 and 5000 characters' },
          { status: 400 }
        );
      }
    }

    // Validate priority if provided
    if (updates.priority !== undefined && (updates.priority < 0 || updates.priority > 10)) {
      return NextResponse.json(
        { success: false, error: 'Priority must be between 0 and 10' },
        { status: 400 }
      );
    }

    // Validate display order if provided
    if (updates.displayOrder !== undefined && updates.displayOrder < 0) {
      return NextResponse.json(
        { success: false, error: 'Display order must be 0 or greater' },
        { status: 400 }
      );
    }

    const updatedFaqs = await updateFAQInTool(toolId, id, updates);

    return NextResponse.json({
      success: true,
      data: updatedFaqs,
    });
  } catch (error) {
    console.error('Error updating FAQ:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update FAQ' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/faqs/[id]
 * Delete a specific FAQ from a tool (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'FAQ ID is required' },
        { status: 400 }
      );
    }

    const { toolId }: { toolId: string } = await request.json();

    if (!toolId) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    const updatedFaqs = await removeFAQFromTool(toolId, id);

    return NextResponse.json({
      success: true,
      data: updatedFaqs,
      message: 'FAQ deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting FAQ:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete FAQ' },
      { status: 500 }
    );
  }
}
