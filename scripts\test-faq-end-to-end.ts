#!/usr/bin/env tsx

/**
 * End-to-End FAQ System Test Script
 * 
 * This script performs comprehensive testing of the FAQ system including:
 * - Database operations
 * - API endpoints
 * - Component integration
 * - Admin workflow
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const adminApiKey = process.env.ADMIN_API_KEY;
const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

interface TestResults {
  databaseOperations: boolean;
  apiEndpoints: boolean;
  dataTransformation: boolean;
  adminWorkflow: boolean;
  publicDisplay: boolean;
}

async function runEndToEndTests(): Promise<TestResults> {
  console.log('🧪 Starting End-to-End FAQ System Tests...\n');

  const results: TestResults = {
    databaseOperations: false,
    apiEndpoints: false,
    dataTransformation: false,
    adminWorkflow: false,
    publicDisplay: false
  };

  let testToolId: string | null = null;

  try {
    // Test 1: Database Operations
    console.log('1️⃣ Testing Database Operations...');
    
    // Create test tool with FAQs
    const testFAQs = [
      {
        id: crypto.randomUUID(),
        question: 'What is this test tool?',
        answer: 'This is a comprehensive test tool for validating the FAQ system.',
        category: 'general',
        displayOrder: 0,
        priority: 5,
        isActive: true,
        isFeatured: true,
        source: 'manual'
      },
      {
        id: crypto.randomUUID(),
        question: 'How much does it cost?',
        answer: 'This test tool is free for testing purposes.',
        category: 'pricing',
        displayOrder: 1,
        priority: 3,
        isActive: true,
        isFeatured: false,
        source: 'manual'
      }
    ];

    const { data: newTool, error: createError } = await supabase
      .from('tools')
      .insert({
        id: `e2e-test-tool-${Date.now()}`,
        name: 'E2E FAQ Test Tool',
        slug: `e2e-faq-test-tool-${Date.now()}`,
        description: 'A tool created for end-to-end FAQ system testing',
        link: `/tools/e2e-faq-test-tool-${Date.now()}`,
        website: 'https://example.com',
        faqs: testFAQs
      })
      .select()
      .single();

    if (createError || !newTool) {
      throw new Error(`Failed to create test tool: ${createError?.message}`);
    }

    testToolId = newTool.id;
    console.log(`✅ Test tool created: ${newTool.name} (ID: ${testToolId})`);
    
    // Verify FAQs were stored correctly
    const { data: toolWithFaqs, error: queryError } = await supabase
      .from('tools')
      .select('id, name, faqs')
      .eq('id', testToolId)
      .single();

    if (queryError || !toolWithFaqs || !Array.isArray(toolWithFaqs.faqs)) {
      throw new Error('Failed to verify FAQ storage');
    }

    console.log(`✅ FAQs stored correctly: ${toolWithFaqs.faqs.length} FAQs`);
    results.databaseOperations = true;

    // Test 2: API Endpoints
    console.log('\n2️⃣ Testing API Endpoints...');
    
    try {
      // Test public API endpoint
      const publicResponse = await fetch(`${appUrl}/api/tools/${testToolId}/faqs`);
      if (publicResponse.ok) {
        const publicData = await publicResponse.json();
        console.log(`✅ Public API works: ${publicData.data?.length || 0} FAQs returned`);
      } else {
        console.warn('⚠️  Public API test failed (app may not be running)');
      }

      // Test admin API endpoint (if admin key available)
      if (adminApiKey) {
        const adminResponse = await fetch(`${appUrl}/api/admin/faqs`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${adminApiKey}`
          },
          body: JSON.stringify({
            toolId: testToolId,
            faqs: [...testFAQs, {
              id: crypto.randomUUID(),
              question: 'Is this system working?',
              answer: 'Yes, the FAQ system is working perfectly!',
              category: 'support',
              displayOrder: 2,
              priority: 4,
              isActive: true,
              isFeatured: false,
              source: 'manual'
            }]
          })
        });

        if (adminResponse.ok) {
          console.log('✅ Admin API works: FAQ update successful');
        } else {
          console.warn('⚠️  Admin API test failed');
        }
      }

      results.apiEndpoints = true;
    } catch (apiError) {
      console.warn('⚠️  API endpoint tests skipped (app not accessible)');
    }

    // Test 3: Data Transformation
    console.log('\n3️⃣ Testing Data Transformation...');
    
    // Test JSONB filtering
    const { data: filteredTools, error: filterError } = await supabase
      .from('tools')
      .select('id, name, faqs')
      .contains('faqs', [{ category: 'general' }]);

    if (!filterError && filteredTools) {
      console.log(`✅ JSONB filtering works: ${filteredTools.length} tools found`);
      results.dataTransformation = true;
    } else {
      console.warn('⚠️  JSONB filtering test failed');
    }

    // Test 4: Admin Workflow Simulation
    console.log('\n4️⃣ Testing Admin Workflow...');
    
    // Simulate admin form data structure
    const adminFormData = {
      name: 'Updated E2E Test Tool',
      description: 'Updated description',
      faqs: [
        {
          question: 'Updated question?',
          answer: 'Updated answer with more details.',
          category: 'general',
          displayOrder: 0,
          priority: 5,
          isActive: true,
          isFeatured: true
        }
      ]
    };

    const { error: updateError } = await supabase
      .from('tools')
      .update({
        name: adminFormData.name,
        description: adminFormData.description,
        faqs: adminFormData.faqs
      })
      .eq('id', testToolId);

    if (!updateError) {
      console.log('✅ Admin workflow simulation successful');
      results.adminWorkflow = true;
    } else {
      console.warn('⚠️  Admin workflow test failed:', updateError.message);
    }

    // Test 5: Public Display Simulation
    console.log('\n5️⃣ Testing Public Display...');
    
    // Simulate ToolQASection component data loading
    const { data: publicTool, error: publicError } = await supabase
      .from('tools')
      .select('id, name, description, faqs')
      .eq('id', testToolId)
      .single();

    if (!publicError && publicTool && publicTool.faqs) {
      const activeFaqs = publicTool.faqs.filter((faq: any) => faq.isActive !== false);
      console.log(`✅ Public display simulation: ${activeFaqs.length} active FAQs`);
      results.publicDisplay = true;
    } else {
      console.warn('⚠️  Public display test failed');
    }

    console.log('\n🎉 End-to-End Tests Completed!');
    
  } catch (error) {
    console.error('\n❌ End-to-End test failed:', error);
  } finally {
    // Cleanup: Remove test tool
    if (testToolId) {
      console.log('\n🧹 Cleaning up test data...');
      
      const { error: deleteError } = await supabase
        .from('tools')
        .delete()
        .eq('id', testToolId);

      if (deleteError) {
        console.warn(`⚠️  Failed to cleanup test tool: ${deleteError.message}`);
      } else {
        console.log('✅ Test data cleaned up successfully');
      }
    }
  }

  return results;
}

async function printTestSummary(results: TestResults) {
  console.log('\n📊 TEST SUMMARY');
  console.log('================');
  console.log(`Database Operations: ${results.databaseOperations ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`API Endpoints: ${results.apiEndpoints ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Data Transformation: ${results.dataTransformation ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Admin Workflow: ${results.adminWorkflow ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Public Display: ${results.publicDisplay ? '✅ PASS' : '❌ FAIL'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall Score: ${passCount}/${totalTests} tests passed`);
  
  if (passCount === totalTests) {
    console.log('🎉 All tests passed! FAQ system is fully functional.');
  } else {
    console.log('⚠️  Some tests failed. Please review the issues above.');
  }
}

// Execute the tests
if (require.main === module) {
  runEndToEndTests()
    .then(printTestSummary)
    .catch(console.error);
}

export { runEndToEndTests };
