'use client';

import React from 'react';
import { ExternalLink, Star, Users } from 'lucide-react';
import { AITool } from '@/lib/types';
import { ResponsiveImage } from '@/components/ui/ResponsiveImage';
import { Tag } from '@/components/ui/Tag';

interface ToolHeroSectionProps {
  tool: AITool;
}

export function ToolHeroSection({ tool }: ToolHeroSectionProps) {
  const handleVisitTool = () => {
    // Use website URL if available, fallback to link for backward compatibility
    const targetUrl = tool.website || tool.link;
    window.open(targetUrl, '_blank', 'noopener,noreferrer');
  };

  return (
    <section className="relative rounded-lg p-8 overflow-hidden" style={{
      background: 'rgba(39, 39, 42, 0.7)', // zinc-800 with 70% opacity
      backdropFilter: 'blur(20px)',
      WebkitBackdropFilter: 'blur(20px)', // Safari support
      border: '1px solid rgba(255, 255, 255, 0.1)',
      boxShadow: `
        0 8px 32px 0 rgba(0, 0, 0, 0.37),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05)
      `
    }}>
      <div className="flex flex-col lg:flex-row gap-6">

        {/* Tool Logo and Basic Info */}
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            <ResponsiveImage
              src={tool.logoUrl}
              alt={`${tool.name} logo`}
              width={80}
              height={80}
              className="rounded-lg border border-zinc-600"
            />
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex flex-wrap items-center gap-2 mb-2">
              <h1 className="text-3xl font-bold text-white">{tool.name}</h1>
              {tool.isVerified && (
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
              {tool.tags?.map((tag, index) => (
                <Tag
                  key={`hero-tag-${index}`}
                  type={tag.type}
                  label={tag.label}
                />
              ))}
            </div>

            {/* Category Hierarchy */}
            <div className="mb-3">
              <span className="text-gray-400 text-sm">
                <span className="capitalize">{tool.category.replace('-', ' ')}</span>
                {tool.subcategory && (
                  <>
                    <span className="mx-2">•</span>
                    <span className="capitalize">{tool.subcategory}</span>
                  </>
                )}
              </span>
            </div>
            
            <p className="text-gray-300 text-lg leading-relaxed mb-4">
              {tool.description}
            </p>
            
            {/* Quick Stats */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-400 mb-6">
              {tool.reviews && (
                <div className="flex items-center gap-1">
                  <Star size={16} className="text-yellow-400 fill-current" />
                  <span className="text-white">{tool.reviews.rating}</span>
                  <span>({tool.reviews.totalReviews} reviews)</span>
                </div>
              )}
              
              {tool.pricing && (
                <div className="flex items-center gap-1">
                  <span className="text-white capitalize">{tool.pricing.type}</span>
                  {tool.pricing.type === 'free' && (
                    <span className="text-green-400">• Free</span>
                  )}
                </div>
              )}
            </div>
            
          </div>
        </div>
        
        {/* CTA Section */}
        <div className="flex-shrink-0 lg:w-64">
          <div className="space-y-3">
            
            {/* Primary CTA Button */}
            <button
              onClick={handleVisitTool}
              className="w-full bg-orange-500 hover:bg-orange-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
              style={{
                backgroundColor: 'rgb(255, 150, 0)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
              }}
            >
              <ExternalLink size={18} />
              Visit {tool.name}
            </button>
            
            {/* Secondary Actions */}
            <div className="grid grid-cols-2 gap-2">
              <button className="bg-zinc-700 hover:bg-zinc-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm">
                Save Tool
              </button>
              <button className="bg-zinc-700 hover:bg-zinc-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm">
                Share
              </button>
            </div>
            
            {/* Social Links */}
            {tool.socialLinks && (
              <div className="pt-3 border-t border-zinc-600">
                <p className="text-gray-400 text-xs mb-2">Follow {tool.name}:</p>
                <div className="flex gap-2">
                  {tool.socialLinks.twitter && (
                    <a
                      href={tool.socialLinks.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-blue-400 transition-colors duration-200"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                    </a>
                  )}
                  {tool.socialLinks.linkedin && (
                    <a
                      href={tool.socialLinks.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-blue-600 transition-colors duration-200"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </a>
                  )}
                  {tool.socialLinks.github && (
                    <a
                      href={tool.socialLinks.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-gray-300 transition-colors duration-200"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                      </svg>
                    </a>
                  )}
                </div>
              </div>
            )}
            
          </div>
        </div>
        
      </div>
    </section>
  );
}
