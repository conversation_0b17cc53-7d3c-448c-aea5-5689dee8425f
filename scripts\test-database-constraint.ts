#!/usr/bin/env tsx

/**
 * Test Database Constraint - Verify content_status constraint
 * 
 * This script tests the current database constraint and shows the SQL
 * that needs to be executed manually in Supabase SQL Editor
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testDatabaseConstraint() {
  console.log('🔍 Testing Database Constraint for content_status...');
  console.log('============================================================');

  try {
    // Test current constraint by trying to insert invalid status
    console.log('📋 Testing current constraint...');
    
    // First, get a tool to test with
    const { data: tools, error: fetchError } = await supabase
      .from('tools')
      .select('id, name, content_status')
      .limit(1);

    if (fetchError || !tools || tools.length === 0) {
      console.error('❌ Could not fetch tools for testing:', fetchError);
      return;
    }

    const testTool = tools[0];
    console.log(`📝 Testing with tool: ${testTool.name} (${testTool.id})`);
    console.log(`   Current status: ${testTool.content_status}`);

    // Test updating to 'under_review' status
    console.log('\n🧪 Testing update to "under_review" status...');
    const { data: updateData, error: updateError } = await supabase
      .from('tools')
      .update({ content_status: 'under_review' })
      .eq('id', testTool.id)
      .select('content_status');

    if (updateError) {
      console.log('❌ Update failed (expected):', updateError.message);
      console.log('   This confirms the constraint is blocking extended status values');
    } else {
      console.log('✅ Update succeeded:', updateData);
      console.log('   This means the constraint already allows extended values');
      
      // Revert the change
      await supabase
        .from('tools')
        .update({ content_status: testTool.content_status })
        .eq('id', testTool.id);
    }

    console.log('\n📋 MANUAL MIGRATION REQUIRED:');
    console.log('============================================================');
    console.log('Execute the following SQL in Supabase SQL Editor:');
    console.log('');
    console.log('-- Drop the existing constraint');
    console.log('ALTER TABLE tools DROP CONSTRAINT IF EXISTS tools_content_status_check;');
    console.log('');
    console.log('-- Add the new constraint with extended values');
    console.log('ALTER TABLE tools ADD CONSTRAINT tools_content_status_check');
    console.log("  CHECK (content_status IN ('draft', 'published', 'archived', 'under_review', 'approved', 'rejected'));");
    console.log('');
    console.log('-- Update the RLS policy to include approved tools as viewable');
    console.log('DROP POLICY IF EXISTS "Published tools are viewable by everyone" ON tools;');
    console.log('CREATE POLICY "Published and approved tools are viewable by everyone"');
    console.log('  ON tools FOR SELECT');
    console.log("  USING (content_status IN ('published', 'approved'));");
    console.log('');
    console.log('-- Add index for better performance on new status values');
    console.log('CREATE INDEX IF NOT EXISTS idx_tools_content_status_workflow ON tools(content_status)');
    console.log("  WHERE content_status IN ('under_review', 'approved', 'rejected');");
    console.log('');
    console.log('-- Add comment for documentation');
    console.log("COMMENT ON COLUMN tools.content_status IS 'Content status supporting editorial workflow: draft, under_review, approved, published, rejected, archived';");
    console.log('');
    console.log('============================================================');
    console.log('');
    console.log('🔗 To access Supabase SQL Editor:');
    console.log('   1. Go to https://supabase.com/dashboard');
    console.log('   2. Select your project');
    console.log('   3. Go to SQL Editor');
    console.log('   4. Paste and execute the SQL above');
    console.log('');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Execute the test
if (require.main === module) {
  testDatabaseConstraint().catch(console.error);
}
