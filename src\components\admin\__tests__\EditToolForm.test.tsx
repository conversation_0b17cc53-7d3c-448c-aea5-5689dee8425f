import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock modules before importing components
jest.mock('@/lib/api', () => ({
  apiClient: {
    getCategories: jest.fn(),
    createAdminTool: jest.fn(),
    updateAdminTool: jest.fn(),
  }
}));

jest.mock('@/lib/validation/tool-uniqueness', () => ({
  checkToolUniqueness: jest.fn(),
  generateUniqueSlug: jest.fn(),
}));

jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null })),
        })),
      })),
    })),
  },
}));

import { EditToolForm } from '../EditToolForm';
import { apiClient } from '@/lib/api';
import * as uniquenessValidation from '@/lib/validation/tool-uniqueness';
import { AITool } from '@/lib/types';
import { transformDbToolToAITool } from '@/lib/data-transformers';

// Get mocked functions
const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Mock uniqueness validation
jest.mock('@/lib/validation/tool-uniqueness');
const mockUniquenessValidation = uniquenessValidation as jest.Mocked<typeof uniquenessValidation>;

// Mock categories
const mockCategories = [
  { id: 'ai-writing', title: 'AI Writing' },
  { id: 'ai-image', title: 'AI Image Generation' },
];

// Mock tool data (DbTool format)
const mockDbTool = {
  id: 'test-tool-123',
  name: 'Test Tool',
  slug: 'test-tool',
  description: 'Test description',
  short_description: 'Short test description',
  detailed_description: 'Detailed test description',
  logo_url: 'https://example.com/logo.png',
  link: '/tools/test-tool',
  website: 'https://test-tool.com',
  category_id: 'ai-writing',
  subcategory: 'text-generation',
  company: 'Test Company',
  is_verified: false,
  is_claimed: false,
  content_status: 'draft',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
  published_at: null,
  features: ['Feature 1', 'Feature 2'],
  screenshots: ['https://example.com/screenshot1.png'],
  pricing: { type: 'freemium', plans: [] },
  social_links: { twitter: 'https://twitter.com/testtool' },
  pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] },
  hashtags: ['ai', 'tool'],
  releases: null,
  haiku: null,
  claim_info: null,
  meta_title: null,
  meta_description: null,
  scraped_data: null,
  ai_generation_status: 'pending',
  last_scraped_at: null,
  editorial_review_id: null,
  ai_generation_job_id: null,
  submission_type: 'admin',
  submission_source: 'admin_panel',
  content_quality_score: null,
  last_ai_update: null,
  generated_content: null,
};

// Convert to AITool for the component
const mockTool: AITool = transformDbToolToAITool(mockDbTool as any);

describe('EditToolForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockApiClient.getCategories.mockResolvedValue(mockCategories);
    mockApiClient.updateAdminTool.mockResolvedValue({
      id: 'test-tool-123',
      name: 'Updated Test Tool',
      slug: 'updated-test-tool',
      description: 'Updated description',
      shortDescription: 'Updated short description',
      detailedDescription: 'Updated detailed description',
      logoUrl: 'https://example.com/logo.png',
      link: '/tools/updated-test-tool',
      website: 'https://updated-test-tool.com',
      category: 'ai-writing',
      subcategory: 'text-generation',
      company: 'Updated Test Company',
      isVerified: false,
      isClaimed: false,
      contentStatus: 'draft',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: new Date().toISOString(),
    });

    // Setup uniqueness validation mocks
    mockUniquenessValidation.checkToolUniqueness.mockResolvedValue({
      isValid: true,
      errors: {},
      conflictingTools: []
    });
  });

  it('renders the form with pre-filled data', async () => {
    render(<EditToolForm tool={mockTool} />);

    await waitFor(() => {
      expect(screen.getByText('Edit AI Tool')).toBeInTheDocument();
    });

    // Expand basic information section
    const basicInfoButton = screen.getByText('Basic Information');
    await userEvent.setup().click(basicInfoButton);

    // Wait for section to expand and check that fields are pre-filled
    await waitFor(() => {
      expect(screen.getByDisplayValue('Test Tool')).toBeInTheDocument();
      expect(screen.getByDisplayValue('https://test-tool.com')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test description')).toBeInTheDocument();
    });
  });

  it('handles data transformation from DbTool to form data correctly', async () => {
    render(<EditToolForm tool={mockTool} />);

    await waitFor(() => {
      expect(screen.getByText('Edit AI Tool')).toBeInTheDocument();
    });

    // Expand content section
    const contentButton = screen.getByText('Content & Features');
    await userEvent.setup().click(contentButton);

    // Wait for section to expand and check that all description fields are properly transformed and displayed
    await waitFor(() => {
      expect(screen.getByDisplayValue('Short test description')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Detailed test description')).toBeInTheDocument();

      // Check that features are properly transformed from array to string
      expect(screen.getByDisplayValue('Feature 1\nFeature 2')).toBeInTheDocument();
    });
  });

  it('validates field transformations on form submission', async () => {
    const user = userEvent.setup();
    render(<EditToolForm tool={mockTool} />);

    await waitFor(() => {
      expect(screen.getByText('Edit AI Tool')).toBeInTheDocument();
    });

    // Expand basic information section
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    // Update basic description
    const descInput = screen.getByDisplayValue('Test description');
    await user.clear(descInput);
    await user.type(descInput, 'Updated test description');

    // Expand content section
    const contentButton = screen.getByText('Content & Features');
    await user.click(contentButton);

    // Update short description
    const shortDescInput = screen.getByDisplayValue('Short test description');
    await user.clear(shortDescInput);
    await user.type(shortDescInput, 'Updated short description');

    // Update detailed description
    const detailedDescInput = screen.getByDisplayValue('Detailed test description');
    await user.clear(detailedDescInput);
    await user.type(detailedDescInput, 'Updated detailed description with more comprehensive information');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /update tool/i });
    await user.click(submitButton);

    // Verify API call with correct data transformation
    await waitFor(() => {
      expect(mockApiClient.updateAdminTool).toHaveBeenCalledWith(
        'test-tool-123',
        expect.objectContaining({
          name: 'Test Tool',
          description: 'Updated test description',
          shortDescription: 'Updated short description',
          detailedDescription: 'Updated detailed description with more comprehensive information',
          category: 'ai-writing', // Should be transformed from category_id
          link: '/tools/test-tool',
        }),
        expect.any(String)
      );
    });
  });

  it('handles uniqueness validation during updates', async () => {
    const user = userEvent.setup();
    
    // Mock uniqueness validation to return conflicts for different tool
    mockUniquenessValidation.checkToolUniqueness.mockResolvedValue({
      isValid: false,
      errors: {
        name: 'A tool named "Conflicting Tool" already exists. Please choose a different name.'
      },
      conflictingTools: [
        {
          field: 'name',
          tool: { id: 'other-tool', name: 'Conflicting Tool', slug: 'conflicting-tool' }
        }
      ]
    });

    render(<EditToolForm tool={mockTool} />);

    await waitFor(() => {
      expect(screen.getByText('Edit AI Tool')).toBeInTheDocument();
    });

    // Change name to conflicting one
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    const nameInput = screen.getByDisplayValue('Test Tool');
    await user.clear(nameInput);
    await user.type(nameInput, 'Conflicting Tool');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /update tool/i });
    await user.click(submitButton);

    // Check for uniqueness validation errors
    await waitFor(() => {
      expect(screen.getByText(/a tool named "conflicting tool" already exists/i)).toBeInTheDocument();
    });

    // Verify API was not called due to validation failure
    expect(mockApiClient.updateAdminTool).not.toHaveBeenCalled();
  });

  it('preserves existing data when not modified', async () => {
    const user = userEvent.setup();
    render(<EditToolForm tool={mockTool} />);

    await waitFor(() => {
      expect(screen.getByText('Edit AI Tool')).toBeInTheDocument();
    });

    // Submit without making changes
    const submitButton = screen.getByRole('button', { name: /update tool/i });
    await user.click(submitButton);

    // Verify that original data is preserved
    await waitFor(() => {
      expect(mockApiClient.updateAdminTool).toHaveBeenCalledWith(
        'test-tool-123',
        expect.objectContaining({
          name: 'Test Tool',
          description: 'Test description',
          shortDescription: 'Short test description',
          detailedDescription: 'Detailed test description',
          features: ['Feature 1', 'Feature 2'], // Should preserve array format
        }),
        expect.any(String)
      );
    });
  });

  it('handles JSONB field updates correctly', async () => {
    const user = userEvent.setup();
    render(<EditToolForm tool={mockTool} />);

    await waitFor(() => {
      expect(screen.getByText('Edit AI Tool')).toBeInTheDocument();
    });

    // Update pricing information
    const pricingButton = screen.getByText('Pricing Information');
    await user.click(pricingButton);

    const pricingSelect = screen.getByDisplayValue('freemium');
    await user.selectOptions(pricingSelect, 'paid');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /update tool/i });
    await user.click(submitButton);

    // Verify JSONB fields are updated correctly
    await waitFor(() => {
      expect(mockApiClient.updateAdminTool).toHaveBeenCalledWith(
        'test-tool-123',
        expect.objectContaining({
          pricing: expect.objectContaining({
            type: 'paid'
          })
        }),
        expect.any(String)
      );
    });
  });

  it('shows success message after successful update', async () => {
    const user = userEvent.setup();
    render(<EditToolForm tool={mockTool} />);

    await waitFor(() => {
      expect(screen.getByText('Edit AI Tool')).toBeInTheDocument();
    });

    // Make a simple change and submit
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    const nameInput = screen.getByDisplayValue('Test Tool');
    await user.clear(nameInput);
    await user.type(nameInput, 'Updated Test Tool');

    const submitButton = screen.getByRole('button', { name: /update tool/i });
    await user.click(submitButton);

    // Check for success message
    await waitFor(() => {
      expect(screen.getByText('Tool Updated Successfully!')).toBeInTheDocument();
    });
  });

  it('handles API errors gracefully', async () => {
    const user = userEvent.setup();
    mockApiClient.updateAdminTool.mockRejectedValue(new Error('Update failed'));

    render(<EditToolForm tool={mockTool} />);

    await waitFor(() => {
      expect(screen.getByText('Edit AI Tool')).toBeInTheDocument();
    });

    // Make a change and submit
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    const nameInput = screen.getByDisplayValue('Test Tool');
    await user.clear(nameInput);
    await user.type(nameInput, 'Updated Test Tool');

    const submitButton = screen.getByRole('button', { name: /update tool/i });
    await user.click(submitButton);

    // Check for error message
    await waitFor(() => {
      expect(screen.getByText('Update failed')).toBeInTheDocument();
    });
  });
});
