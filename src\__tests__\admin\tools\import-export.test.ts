/**
 * @jest-environment node
 */

// Mock Next.js Request before importing
global.Request = class MockRequest {
  constructor(public url: string, public init?: RequestInit) {}
  headers = new Map();
  method = 'GET';
  body = null;
  formData = async () => new FormData();
  text = async () => '';
  json = async () => ({});
} as any;

import { NextRequest } from 'next/server';
import { GET } from '@/app/api/admin/tools/export/route';
import { POST } from '@/app/api/admin/tools/import/route';

// Mock dependencies
jest.mock('@/lib/supabase', () => ({
  getAdminTools: jest.fn(),
  createTool: jest.fn(),
}));

jest.mock('@/lib/auth', () => ({
  validateApiKey: jest.fn(),
}));

const mockGetAdminTools = require('@/lib/supabase').getAdminTools;
const mockCreateTool = require('@/lib/supabase').createTool;
const mockValidateApiKey = require('@/lib/auth').validateApiKey;

// Sample tool data for testing
const sampleTools = [
  {
    id: 'tool-1',
    name: 'Test Tool 1',
    slug: 'test-tool-1',
    description: 'A test tool for testing',
    short_description: 'Test tool',
    detailed_description: 'Detailed description of test tool',
    link: '/tools/test-tool-1',
    website: 'https://testtool1.com',
    category_id: 'ai-tools',
    subcategory: 'chatbots',
    company: 'Test Company',
    is_verified: true,
    is_claimed: false,
    content_status: 'published',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    features: { key: 'value' },
    pricing: { type: 'free' },
  },
  {
    id: 'tool-2',
    name: 'Test Tool 2',
    slug: 'test-tool-2',
    description: 'Another test tool',
    short_description: 'Another test',
    detailed_description: 'Another detailed description',
    link: '/tools/test-tool-2',
    website: 'https://testtool2.com',
    category_id: 'ai-tools',
    subcategory: 'writing',
    company: 'Another Company',
    is_verified: false,
    is_claimed: true,
    content_status: 'draft',
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    features: null,
    pricing: null,
  },
];

describe('Tool Export API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockValidateApiKey.mockResolvedValue(true);
    mockGetAdminTools.mockResolvedValue({ data: sampleTools });
  });

  describe('GET /api/admin/tools/export', () => {
    it('should export tools as JSON by default', async () => {
      const request = new NextRequest('http://localhost/api/admin/tools/export');
      const response = await GET(request);

      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toBe('application/json');
      expect(response.headers.get('content-disposition')).toContain('attachment');
      expect(response.headers.get('content-disposition')).toContain('.json');

      const responseText = await response.text();
      const exportData = JSON.parse(responseText);

      expect(exportData).toHaveProperty('exportedAt');
      expect(exportData).toHaveProperty('totalTools', 2);
      expect(exportData).toHaveProperty('tools');
      expect(exportData.tools).toHaveLength(2);
      expect(exportData.tools[0]).toMatchObject(sampleTools[0]);
    });

    it('should export tools as CSV when format=csv', async () => {
      const request = new NextRequest('http://localhost/api/admin/tools/export?format=csv');
      const response = await GET(request);

      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toBe('text/csv');
      expect(response.headers.get('content-disposition')).toContain('attachment');
      expect(response.headers.get('content-disposition')).toContain('.csv');

      const csvContent = await response.text();
      const lines = csvContent.split('\n');

      // Check header row
      expect(lines[0]).toContain('Id,Name,Slug,Description');
      
      // Check data rows
      expect(lines[1]).toContain('tool-1,"Test Tool 1",test-tool-1');
      expect(lines[2]).toContain('tool-2,"Test Tool 2",test-tool-2');
    });

    it('should filter tools by category', async () => {
      const request = new NextRequest('http://localhost/api/admin/tools/export?category=ai-tools');
      await GET(request);

      expect(mockGetAdminTools).toHaveBeenCalledWith({
        category: 'ai-tools'
      });
    });

    it('should filter tools by status', async () => {
      const request = new NextRequest('http://localhost/api/admin/tools/export?status=published');
      await GET(request);

      expect(mockGetAdminTools).toHaveBeenCalledWith({
        contentStatus: 'published'
      });
    });

    it('should filter tools by date range', async () => {
      const request = new NextRequest('http://localhost/api/admin/tools/export?dateFrom=2024-01-01&dateTo=2024-01-31');
      await GET(request);

      expect(mockGetAdminTools).toHaveBeenCalledWith({
        createdAfter: '2024-01-01',
        createdBefore: '2024-01-31'
      });
    });

    it('should return 401 when API key is invalid', async () => {
      mockValidateApiKey.mockResolvedValue(false);
      
      const request = new NextRequest('http://localhost/api/admin/tools/export');
      const response = await GET(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 400 for invalid format', async () => {
      const request = new NextRequest('http://localhost/api/admin/tools/export?format=xml');
      const response = await GET(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid format');
    });

    it('should handle export errors gracefully', async () => {
      mockGetAdminTools.mockRejectedValue(new Error('Database error'));
      
      const request = new NextRequest('http://localhost/api/admin/tools/export');
      const response = await GET(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to export tools');
    });
  });
});

describe('Tool Import API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockValidateApiKey.mockResolvedValue(true);
    mockGetAdminTools.mockResolvedValue({ data: [] }); // No existing tools
    mockCreateTool.mockResolvedValue({ id: 'new-tool-id' });
  });

  describe('POST /api/admin/tools/import', () => {
    it('should return 401 when API key is invalid', async () => {
      mockValidateApiKey.mockResolvedValue(false);
      
      const formData = new FormData();
      const request = new NextRequest('http://localhost/api/admin/tools/import', {
        method: 'POST',
        body: formData,
      });
      
      const response = await POST(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 400 when no file is provided', async () => {
      const formData = new FormData();
      const request = new NextRequest('http://localhost/api/admin/tools/import', {
        method: 'POST',
        body: formData,
      });
      
      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('No file provided');
    });

    it('should return 400 for invalid file type', async () => {
      const formData = new FormData();
      const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
      formData.append('file', file);
      
      const request = new NextRequest('http://localhost/api/admin/tools/import', {
        method: 'POST',
        body: formData,
      });
      
      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid file type');
    });

    it('should return preview data when preview=true', async () => {
      const jsonData = {
        tools: [
          {
            name: 'Test Tool',
            link: '/tools/test-tool',
            description: 'A test tool'
          }
        ]
      };
      
      const formData = new FormData();
      const file = new File([JSON.stringify(jsonData)], 'tools.json', { type: 'application/json' });
      formData.append('file', file);
      formData.append('preview', 'true');
      
      const request = new NextRequest('http://localhost/api/admin/tools/import', {
        method: 'POST',
        body: formData,
      });
      
      const response = await POST(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.preview).toBe(true);
      expect(data.totalRows).toBe(1);
      expect(data.sampleData).toHaveLength(1);
    });

    it('should handle JSON import with valid data', async () => {
      const jsonData = {
        tools: [
          {
            name: 'Test Tool',
            link: '/tools/test-tool',
            description: 'A test tool',
            category_id: 'ai-tools'
          }
        ]
      };
      
      const formData = new FormData();
      const file = new File([JSON.stringify(jsonData)], 'tools.json', { type: 'application/json' });
      formData.append('file', file);
      formData.append('duplicateStrategy', 'skip');
      
      const request = new NextRequest('http://localhost/api/admin/tools/import', {
        method: 'POST',
        body: formData,
      });
      
      const response = await POST(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.result.imported).toBe(1);
      expect(data.result.errors).toHaveLength(0);
      expect(mockCreateTool).toHaveBeenCalledTimes(1);
    });

    it('should handle CSV import with valid data', async () => {
      const csvData = `Name,Link,Description,Category Id
Test Tool,/tools/test-tool,A test tool,ai-tools`;
      
      const formData = new FormData();
      const file = new File([csvData], 'tools.csv', { type: 'text/csv' });
      formData.append('file', file);
      formData.append('duplicateStrategy', 'skip');
      
      const request = new NextRequest('http://localhost/api/admin/tools/import', {
        method: 'POST',
        body: formData,
      });
      
      const response = await POST(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.result.imported).toBe(1);
      expect(mockCreateTool).toHaveBeenCalledTimes(1);
    });

    it('should handle duplicate detection', async () => {
      // Mock existing tools
      mockGetAdminTools.mockResolvedValue({
        data: [{ name: 'Existing Tool', slug: 'existing-tool' }]
      });
      
      const jsonData = {
        tools: [
          {
            name: 'Existing Tool',
            link: '/tools/existing-tool',
            description: 'This tool already exists'
          }
        ]
      };
      
      const formData = new FormData();
      const file = new File([JSON.stringify(jsonData)], 'tools.json', { type: 'application/json' });
      formData.append('file', file);
      formData.append('duplicateStrategy', 'skip');
      
      const request = new NextRequest('http://localhost/api/admin/tools/import', {
        method: 'POST',
        body: formData,
      });
      
      const response = await POST(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.result.imported).toBe(0);
      expect(data.result.skipped).toBe(1);
      expect(data.result.duplicates).toHaveLength(1);
      expect(mockCreateTool).not.toHaveBeenCalled();
    });

    it('should handle import errors gracefully', async () => {
      const jsonData = {
        tools: [
          {
            // Missing required fields
            description: 'Tool without name or link'
          }
        ]
      };
      
      const formData = new FormData();
      const file = new File([JSON.stringify(jsonData)], 'tools.json', { type: 'application/json' });
      formData.append('file', file);
      
      const request = new NextRequest('http://localhost/api/admin/tools/import', {
        method: 'POST',
        body: formData,
      });
      
      const response = await POST(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.result.imported).toBe(0);
      expect(data.result.errors).toHaveLength(1);
      expect(data.result.errors[0].error).toContain('Missing required fields');
    });
  });
});
