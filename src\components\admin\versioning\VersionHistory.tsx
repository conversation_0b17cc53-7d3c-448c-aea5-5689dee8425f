'use client';

import { useState, useEffect } from 'react';
import { Clock, User, GitBranch, RotateCcw, Eye, GitCompare } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { ToolVersion, VersionListResponse, VersionChangeType } from '@/lib/types/versioning';
import { apiClient } from '@/lib/api';

interface VersionHistoryProps {
  toolId: string;
  onRollback?: (version: ToolVersion) => void;
  onCompare?: (fromVersion: number, toVersion: number) => void;
  onViewVersion?: (version: ToolVersion) => void;
}

export function VersionHistory({ 
  toolId, 
  onRollback, 
  onCompare, 
  onViewVersion 
}: VersionHistoryProps) {
  const [versions, setVersions] = useState<ToolVersion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedVersions, setSelectedVersions] = useState<number[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  useEffect(() => {
    fetchVersionHistory();
  }, [toolId, pagination.page]);

  const fetchVersionHistory = async () => {
    try {
      setLoading(true);
      setError(null);

      const adminApiKey = 'aidude_admin_2024_secure_key_xyz789'; // This should be passed as a prop in production
      
      const response = await fetch(`/api/admin/tools/${toolId}/versions?page=${pagination.page}&limit=${pagination.limit}`, {
        headers: {
          'x-api-key': adminApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch version history');
      }

      const result = await response.json();
      
      if (result.success) {
        const versionData: VersionListResponse = result.data;
        setVersions(versionData.versions);
        setPagination(versionData.pagination);
      } else {
        throw new Error(result.error || 'Failed to fetch version history');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch version history');
    } finally {
      setLoading(false);
    }
  };

  const handleVersionSelect = (versionNumber: number) => {
    setSelectedVersions(prev => {
      if (prev.includes(versionNumber)) {
        return prev.filter(v => v !== versionNumber);
      } else if (prev.length < 2) {
        return [...prev, versionNumber];
      } else {
        // Replace the first selected version
        return [prev[1], versionNumber];
      }
    });
  };

  const handleCompareSelected = () => {
    if (selectedVersions.length === 2 && onCompare) {
      const [from, to] = selectedVersions.sort((a, b) => a - b);
      onCompare(from, to);
    }
  };

  const getChangeTypeColor = (changeType: VersionChangeType): string => {
    switch (changeType) {
      case 'create':
        return 'bg-green-600';
      case 'update':
        return 'bg-blue-600';
      case 'rollback':
        return 'bg-orange-600';
      case 'bulk_update':
        return 'bg-purple-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getChangeTypeIcon = (changeType: VersionChangeType) => {
    switch (changeType) {
      case 'create':
        return <GitBranch className="w-4 h-4" />;
      case 'update':
        return <Clock className="w-4 h-4" />;
      case 'rollback':
        return <RotateCcw className="w-4 h-4" />;
      case 'bulk_update':
        return <GitBranch className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="bg-zinc-800 rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-zinc-700 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-zinc-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-zinc-800 rounded-lg p-6">
        <div className="text-red-400 text-center">
          <p className="mb-4">Error loading version history: {error}</p>
          <Button onClick={fetchVersionHistory} variant="outline">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-zinc-800 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">Version History</h3>
        <div className="flex items-center gap-2">
          {selectedVersions.length === 2 && (
            <Button
              onClick={handleCompareSelected}
              className="bg-blue-600 hover:bg-blue-700"
              size="sm"
            >
              <GitCompare className="w-4 h-4 mr-2" />
              Compare Selected
            </Button>
          )}
          <span className="text-sm text-gray-400">
            {pagination.total} version{pagination.total !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      {versions.length === 0 ? (
        <div className="text-center text-gray-400 py-8">
          <GitBranch className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No versions found for this tool</p>
        </div>
      ) : (
        <div className="space-y-3">
          {versions.map((version) => (
            <div
              key={version.id}
              className={`border rounded-lg p-4 transition-all duration-200 cursor-pointer ${
                selectedVersions.includes(version.versionNumber)
                  ? 'border-orange-500 bg-orange-500/10'
                  : 'border-zinc-700 hover:border-zinc-600'
              } ${version.isCurrent ? 'ring-2 ring-green-500/50' : ''}`}
              onClick={() => handleVersionSelect(version.versionNumber)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${getChangeTypeColor(version.changeType)}`}>
                    {getChangeTypeIcon(version.changeType)}
                  </div>
                  
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-white">
                        Version {version.versionNumber}
                      </span>
                      {version.isCurrent && (
                        <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-full">
                          Current
                        </span>
                      )}
                      <span className="px-2 py-1 bg-zinc-700 text-gray-300 text-xs rounded-full capitalize">
                        {version.changeType.replace('_', ' ')}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-400 mt-1">
                      {version.changeSummary || 'No description provided'}
                    </p>
                    
                    <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        {version.createdBy}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {new Date(version.createdAt).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {onViewVersion && (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewVersion(version);
                      }}
                      className="px-3 py-1 border border-zinc-600 rounded text-gray-400 hover:text-white hover:border-zinc-500 transition-colors"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  )}

                  {!version.isCurrent && onRollback && (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        onRollback(version);
                      }}
                      className="px-3 py-1 border border-orange-400 rounded text-orange-400 hover:bg-orange-400 hover:text-white transition-colors"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>

              {version.rollbackReason && (
                <div className="mt-3 p-3 bg-orange-500/10 border border-orange-500/20 rounded">
                  <p className="text-sm text-orange-300">
                    <strong>Rollback reason:</strong> {version.rollbackReason}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-zinc-700">
          <div className="text-sm text-gray-400">
            Page {pagination.page} of {pagination.totalPages}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page <= 1}
              variant="outline"
              size="sm"
            >
              Previous
            </Button>
            
            <Button
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page >= pagination.totalPages}
              variant="outline"
              size="sm"
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {selectedVersions.length > 0 && (
        <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded">
          <p className="text-sm text-blue-300">
            {selectedVersions.length === 1 
              ? `Version ${selectedVersions[0]} selected`
              : `Versions ${selectedVersions.join(' and ')} selected for comparison`
            }
          </p>
        </div>
      )}
    </div>
  );
}
