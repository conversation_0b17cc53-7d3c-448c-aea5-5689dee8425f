#!/usr/bin/env tsx

/**
 * Production Health Check Script
 * 
 * Comprehensive health check for production deployment
 * Tests all critical systems and reports status
 */

import { createClient } from '@supabase/supabase-js';
import { getJobQueue } from '../src/lib/jobs';
import OpenAI from 'openai';
import nodemailer from 'nodemailer';

interface HealthCheck {
  name: string;
  status: 'healthy' | 'warning' | 'error';
  message: string;
  responseTime?: number;
  details?: any;
}

class ProductionHealthChecker {
  private results: HealthCheck[] = [];

  async runAllChecks(): Promise<void> {
    console.log('🏥 Starting Production Health Check...\n');

    await this.checkEnvironmentVariables();
    await this.checkDatabase();
    await this.checkJobQueue();
    await this.checkOpenAI();
    await this.checkEmail();
    await this.checkAPIEndpoints();

    this.printResults();
  }

  private async checkEnvironmentVariables(): Promise<void> {
    const start = Date.now();
    
    const requiredVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY',
      'OPENAI_API_KEY',
      'JOB_QUEUE_ENABLED',
      'SMTP_HOST',
      'SMTP_USER',
      'ADMIN_EMAIL',
      'JWT_SECRET',
      'ADMIN_API_KEY'
    ];

    const missing = requiredVars.filter(varName => !process.env[varName]);
    const responseTime = Date.now() - start;

    if (missing.length === 0) {
      this.results.push({
        name: 'Environment Variables',
        status: 'healthy',
        message: 'All required environment variables are set',
        responseTime,
      });
    } else {
      this.results.push({
        name: 'Environment Variables',
        status: 'error',
        message: `Missing required variables: ${missing.join(', ')}`,
        responseTime,
        details: { missing }
      });
    }
  }

  private async checkDatabase(): Promise<void> {
    const start = Date.now();
    
    try {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );

      // Test basic query
      const { data, error } = await supabase
        .from('categories')
        .select('count')
        .limit(1);

      const responseTime = Date.now() - start;

      if (error) {
        this.results.push({
          name: 'Database Connection',
          status: 'error',
          message: `Database query failed: ${error.message}`,
          responseTime,
          details: { error }
        });
      } else {
        this.results.push({
          name: 'Database Connection',
          status: 'healthy',
          message: 'Database connection and queries working',
          responseTime,
        });
      }
    } catch (error) {
      const responseTime = Date.now() - start;
      this.results.push({
        name: 'Database Connection',
        status: 'error',
        message: `Database connection failed: ${error}`,
        responseTime,
        details: { error }
      });
    }
  }

  private async checkJobQueue(): Promise<void> {
    const start = Date.now();
    
    try {
      if (process.env.JOB_QUEUE_ENABLED !== 'true') {
        this.results.push({
          name: 'Job Queue',
          status: 'warning',
          message: 'Job queue is disabled',
          responseTime: Date.now() - start,
        });
        return;
      }

      const queue = getJobQueue();
      const jobs = await queue.getJobs();
      const responseTime = Date.now() - start;

      const pendingJobs = jobs.filter(job => job.status === 'pending').length;
      const failedJobs = jobs.filter(job => job.status === 'failed').length;

      let status: 'healthy' | 'warning' | 'error' = 'healthy';
      let message = `Job queue operational. ${jobs.length} total jobs`;

      if (failedJobs > 10) {
        status = 'error';
        message += `, ${failedJobs} failed jobs (high failure rate)`;
      } else if (failedJobs > 0) {
        status = 'warning';
        message += `, ${failedJobs} failed jobs`;
      }

      if (pendingJobs > 50) {
        status = 'warning';
        message += `, ${pendingJobs} pending jobs (high backlog)`;
      }

      this.results.push({
        name: 'Job Queue',
        status,
        message,
        responseTime,
        details: {
          totalJobs: jobs.length,
          pendingJobs,
          failedJobs,
          maxConcurrent: process.env.MAX_CONCURRENT_JOBS || '3'
        }
      });
    } catch (error) {
      const responseTime = Date.now() - start;
      this.results.push({
        name: 'Job Queue',
        status: 'error',
        message: `Job queue check failed: ${error}`,
        responseTime,
        details: { error }
      });
    }
  }

  private async checkOpenAI(): Promise<void> {
    const start = Date.now();
    
    try {
      if (!process.env.OPENAI_API_KEY) {
        this.results.push({
          name: 'OpenAI API',
          status: 'error',
          message: 'OpenAI API key not configured',
          responseTime: Date.now() - start,
        });
        return;
      }

      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });

      // Test with a minimal request
      const response = await openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-4o',
        messages: [{ role: 'user', content: 'Test' }],
        max_tokens: 5,
      });

      const responseTime = Date.now() - start;

      if (response.choices && response.choices.length > 0) {
        this.results.push({
          name: 'OpenAI API',
          status: 'healthy',
          message: 'OpenAI API responding correctly',
          responseTime,
          details: {
            model: response.model,
            usage: response.usage
          }
        });
      } else {
        this.results.push({
          name: 'OpenAI API',
          status: 'warning',
          message: 'OpenAI API responded but with unexpected format',
          responseTime,
          details: { response }
        });
      }
    } catch (error: any) {
      const responseTime = Date.now() - start;
      let status: 'warning' | 'error' = 'error';
      let message = `OpenAI API failed: ${error.message}`;

      // Check for rate limiting or quota issues
      if (error.status === 429) {
        status = 'warning';
        message = 'OpenAI API rate limited (temporary)';
      } else if (error.status === 401) {
        message = 'OpenAI API authentication failed (check API key)';
      }

      this.results.push({
        name: 'OpenAI API',
        status,
        message,
        responseTime,
        details: { 
          error: error.message,
          status: error.status,
          type: error.type
        }
      });
    }
  }

  private async checkEmail(): Promise<void> {
    const start = Date.now();
    
    try {
      if (!process.env.SMTP_HOST || !process.env.SMTP_USER) {
        this.results.push({
          name: 'Email Service',
          status: 'warning',
          message: 'Email service not configured',
          responseTime: Date.now() - start,
        });
        return;
      }

      const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_PORT === '465',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });

      // Verify SMTP connection
      await transporter.verify();
      const responseTime = Date.now() - start;

      this.results.push({
        name: 'Email Service',
        status: 'healthy',
        message: 'SMTP connection verified',
        responseTime,
        details: {
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT,
          user: process.env.SMTP_USER
        }
      });
    } catch (error) {
      const responseTime = Date.now() - start;
      this.results.push({
        name: 'Email Service',
        status: 'error',
        message: `Email service failed: ${error}`,
        responseTime,
        details: { error }
      });
    }
  }

  private async checkAPIEndpoints(): Promise<void> {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    
    const endpoints = [
      '/api/categories',
      '/api/tools',
      '/api/health'
    ];

    for (const endpoint of endpoints) {
      const start = Date.now();
      
      try {
        const response = await fetch(`${baseUrl}${endpoint}`);
        const responseTime = Date.now() - start;

        if (response.ok) {
          this.results.push({
            name: `API ${endpoint}`,
            status: 'healthy',
            message: `Endpoint responding correctly (${response.status})`,
            responseTime,
          });
        } else {
          this.results.push({
            name: `API ${endpoint}`,
            status: 'error',
            message: `Endpoint returned ${response.status}: ${response.statusText}`,
            responseTime,
          });
        }
      } catch (error) {
        const responseTime = Date.now() - start;
        this.results.push({
          name: `API ${endpoint}`,
          status: 'error',
          message: `Endpoint unreachable: ${error}`,
          responseTime,
          details: { error }
        });
      }
    }
  }

  private printResults(): void {
    console.log('\n📊 Health Check Results:\n');
    
    const healthy = this.results.filter(r => r.status === 'healthy').length;
    const warnings = this.results.filter(r => r.status === 'warning').length;
    const errors = this.results.filter(r => r.status === 'error').length;

    this.results.forEach(result => {
      const icon = result.status === 'healthy' ? '✅' : 
                   result.status === 'warning' ? '⚠️' : '❌';
      const time = result.responseTime ? ` (${result.responseTime}ms)` : '';
      
      console.log(`${icon} ${result.name}: ${result.message}${time}`);
      
      if (result.details && (result.status === 'warning' || result.status === 'error')) {
        console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
      }
    });

    console.log(`\n📈 Summary: ${healthy} healthy, ${warnings} warnings, ${errors} errors`);
    
    if (errors > 0) {
      console.log('\n🚨 Critical issues found! Address errors before production deployment.');
      process.exit(1);
    } else if (warnings > 0) {
      console.log('\n⚠️ Some warnings found. Review before production deployment.');
      process.exit(0);
    } else {
      console.log('\n🎉 All systems healthy! Ready for production.');
      process.exit(0);
    }
  }
}

// Run health check if called directly
if (require.main === module) {
  const checker = new ProductionHealthChecker();
  checker.runAllChecks().catch(error => {
    console.error('Health check failed:', error);
    process.exit(1);
  });
}

export { ProductionHealthChecker };
