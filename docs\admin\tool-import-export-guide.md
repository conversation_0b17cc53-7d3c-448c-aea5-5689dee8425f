# Tool Import/Export Guide

## Overview

The Tool Import/Export functionality allows administrators to efficiently manage tool data in bulk through CSV and JSON file formats. This feature supports both importing new tools and exporting existing tool data with comprehensive filtering options.

## Features

### Export Functionality
- **Multiple Formats**: Export data in JSON or CSV format
- **Filtering Options**: Filter by category, status, verification status, and date ranges
- **Field Selection**: Choose specific fields to include in exports
- **Automatic Downloads**: Generated files are automatically downloaded with proper naming

### Import Functionality
- **File Format Support**: Import from CSV or JSON files
- **Data Validation**: Comprehensive validation of imported data
- **Duplicate Detection**: Configurable strategies for handling duplicate tools
- **Preview Mode**: Preview import data before committing changes
- **Error Reporting**: Detailed error reporting for failed imports

## Export Guide

### Using the Export Feature

1. **Access the Import/Export Panel**
   - Navigate to Admin Dashboard or Tool Management page
   - Click "Import/Export Tools" button
   - The Import/Export panel will appear

2. **Choose Export Format**
   - Click "Export JSON" for structured data export
   - Click "Export CSV" for spreadsheet-compatible export

3. **Apply Filters (Optional)**
   - Use URL parameters to filter exported data:
   - `?category=ai-tools` - Filter by category
   - `?status=published` - Filter by content status
   - `?verified=true` - Filter by verification status
   - `?dateFrom=2024-01-01&dateTo=2024-12-31` - Filter by date range
   - `?fields=name,description,link` - Select specific fields

### Export Formats

#### JSON Export
```json
{
  "exportedAt": "2024-06-18T10:00:00Z",
  "totalTools": 100,
  "tools": [
    {
      "id": "tool-1",
      "name": "Tool Name",
      "slug": "tool-name",
      "description": "Tool description",
      "link": "/tools/tool-name",
      "website": "https://example.com",
      "category_id": "ai-tools",
      "content_status": "published",
      "features": {...},
      "pricing": {...}
    }
  ]
}
```

#### CSV Export
- Headers are automatically generated from field names
- JSON fields are serialized as escaped JSON strings
- Boolean values are exported as "true"/"false"
- Null values are exported as empty strings

## Import Guide

### Supported File Formats

#### JSON Format Options

1. **Simple Format** (URL list):
```json
{
  "urls": [
    "https://example.com/tool1",
    "https://example.com/tool2"
  ]
}
```

2. **Detailed Format** (Full tool data):
```json
{
  "tools": [
    {
      "name": "Tool Name",
      "link": "/tools/tool-name",
      "description": "Tool description",
      "category_id": "ai-tools",
      "features": {...},
      "pricing": {...}
    }
  ]
}
```

3. **Mapping Format** (Custom field mapping):
```json
{
  "fieldMapping": {
    "name": "tool_name",
    "url": "website_url",
    "description": "tool_description"
  },
  "data": [
    {
      "tool_name": "Tool Name",
      "website_url": "https://example.com",
      "tool_description": "Description"
    }
  ]
}
```

#### CSV Format
- First row must contain headers
- Headers are automatically mapped to database fields
- JSON fields can be included as escaped JSON strings
- Boolean fields accept "true"/"false" or "1"/"0"

### Import Process

1. **Select File**
   - Click "Import File" button
   - Choose CSV or JSON file (max 50MB)
   - File is automatically processed for preview

2. **Preview Data**
   - Review the import preview dialog
   - Check total number of tools to be imported
   - Review sample data to ensure correct formatting

3. **Configure Duplicate Handling**
   - **Skip duplicates**: Ignore tools that already exist
   - **Error on duplicates**: Stop import if duplicates are found
   - **Update existing**: Replace existing tools (not yet implemented)

4. **Confirm Import**
   - Click "Confirm Import" to proceed
   - Monitor progress and review results

### Import Results

After import completion, you'll see:
- **Imported**: Number of successfully imported tools
- **Skipped**: Number of tools skipped (usually duplicates)
- **Errors**: Number of tools that failed to import
- **Error Details**: Expandable list of specific errors

## Data Validation

### Required Fields
- `name`: Tool name (string, required)
- `link`: Internal tool page link (string, required)

### Optional Fields
- `description`: Tool description
- `short_description`: Brief description (max 150 chars)
- `website`: External website URL
- `category_id`: Tool category
- `subcategory`: Tool subcategory
- `company`: Company name
- `is_verified`: Verification status (boolean)
- `is_claimed`: Claim status (boolean)
- `content_status`: Content status (draft/published/archived)
- `features`: Features data (JSON)
- `pricing`: Pricing information (JSON)

### Validation Rules
- URLs must be valid HTTP/HTTPS URLs
- Slugs are auto-generated from names if not provided
- JSON fields must be valid JSON or will be treated as strings
- Boolean fields accept various formats (true/false, 1/0, yes/no)

## Error Handling

### Common Import Errors
- **Missing required fields**: Name or link not provided
- **Invalid URL format**: Malformed URLs in link or website fields
- **Duplicate detection**: Tool with same name or slug already exists
- **Invalid JSON**: Malformed JSON in features or pricing fields
- **File size exceeded**: File larger than 50MB limit
- **Invalid file format**: File extension not .csv or .json

### Troubleshooting
1. **Check file format**: Ensure file has correct extension
2. **Validate JSON**: Use JSON validator for JSON files
3. **Check required fields**: Ensure name and link are provided
4. **Review duplicates**: Check existing tools for conflicts
5. **Reduce file size**: Split large files into smaller batches

## Best Practices

### For Exports
- Use filters to export only needed data
- Choose appropriate format for your use case
- JSON for programmatic processing
- CSV for spreadsheet applications

### For Imports
- Start with small test files
- Use preview mode to validate data
- Keep backup of original data
- Handle duplicates appropriately
- Review error reports carefully

### Data Preparation
- Clean data before import
- Ensure consistent formatting
- Validate URLs before import
- Use standard category names
- Include all required fields

## API Endpoints

### Export API
```
GET /api/admin/tools/export?format=json&category=ai-tools
Headers: x-api-key: YOUR_API_KEY
```

### Import API
```
POST /api/admin/tools/import
Headers: x-api-key: YOUR_API_KEY
Body: FormData with file and options
```

## Security Considerations

- Import/export requires admin API key
- File size limits prevent abuse
- Data validation prevents injection attacks
- All operations are logged for audit trails

## Sample Files

Sample import files are available in the `docs/sample-data/` directory:
- `tools-import-sample.json`: Example JSON import file
- `tools-import-sample.csv`: Example CSV import file

These files demonstrate proper formatting and can be used for testing the import functionality.
