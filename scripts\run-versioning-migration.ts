#!/usr/bin/env tsx

/**
 * Tool Versioning System Migration Runner
 * Applies the versioning schema migration and initializes versions for existing tools
 */

import { readFileSync } from 'fs';
import { join } from 'path';
import { createClient } from '@supabase/supabase-js';

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   - SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function runVersioningMigration() {
  console.log('🚀 Starting Tool Versioning System Migration...\n');

  try {
    // Step 1: Read and execute the main migration
    console.log('📋 Step 1: Applying versioning schema migration...');
    const migrationPath = join(process.cwd(), 'src/lib/database/migrations/003_tool_versioning_schema.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf-8');

    console.log('⚠️  Manual SQL execution required. Please run the following SQL in your Supabase SQL editor:');
    console.log('\n--- COPY AND PASTE THE FOLLOWING SQL ---');
    console.log(migrationSQL);
    console.log('--- END OF SQL ---\n');

    console.log('After running the SQL, press Enter to continue...');
    await new Promise(resolve => {
      process.stdin.once('data', () => resolve(undefined));
    });

    console.log('✅ Versioning schema migration completed successfully');

    // Step 2: Apply rollback functions
    console.log('\n📋 Step 2: Installing rollback functions...');
    const rollbackFunctionPath = join(process.cwd(), 'src/lib/database/rollback-function.sql');
    const rollbackSQL = readFileSync(rollbackFunctionPath, 'utf-8');

    console.log('⚠️  Manual SQL execution required. Please run the following SQL in your Supabase SQL editor:');
    console.log('\n--- COPY AND PASTE THE FOLLOWING SQL ---');
    console.log(rollbackSQL);
    console.log('--- END OF SQL ---\n');

    console.log('After running the SQL, press Enter to continue...');
    await new Promise(resolve => {
      process.stdin.once('data', () => resolve(undefined));
    });

    console.log('✅ Rollback functions installed successfully');

    // Step 3: Initialize versions for existing tools
    console.log('\n📋 Step 3: Initializing versions for existing tools...');

    try {
      const { data: initResult, error: initError } = await supabase.rpc('initialize_tool_versions');

      if (initError) {
        console.warn('⚠️  Could not initialize tool versions automatically:', initError.message);
        console.log('You may need to run the initialization manually after the migration.');
      } else {
        console.log(`✅ Initialized versions for ${initResult || 0} existing tools`);
      }
    } catch (error) {
      console.warn('⚠️  Tool version initialization will need to be done manually:', error);
    }

    // Step 4: Verify migration
    console.log('\n📋 Step 4: Verifying migration...');
    
    // Check if tables exist
    const tables = ['tool_versions', 'version_audit_log', 'version_comparisons'];
    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.error(`❌ Table ${table} verification failed:`, error.message);
        throw error;
      }
      
      console.log(`✅ Table ${table} is accessible`);
    }

    // Check if functions exist by testing with a non-existent tool
    try {
      const { error: funcError } = await supabase.rpc('rollback_tool_to_version', {
        p_tool_id: 'test-non-existent',
        p_target_version_data: { name: 'test' },
        p_rollback_reason: 'test',
        p_performed_by: 'test'
      });

      // This should fail with tool not found, but function should exist
      if (funcError && funcError.message.includes('Tool not found')) {
        console.log('✅ Rollback function is accessible');
      } else if (funcError) {
        console.warn('⚠️  Rollback function verification warning:', funcError.message);
      } else {
        console.log('✅ Rollback function is accessible');
      }
    } catch (error) {
      console.warn('⚠️  Could not verify rollback function:', error);
    }

    // Step 5: Update migration tracking
    console.log('\n📋 Step 5: Recording migration...');

    // Create migrations table if it doesn't exist
    const { error: createTableError } = await supabase.rpc('sql', {
      query: `
        CREATE TABLE IF NOT EXISTS migrations (
          id VARCHAR(255) PRIMARY KEY,
          executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
      `
    });

    if (createTableError) {
      console.warn('⚠️  Could not create migrations table:', createTableError.message);
    }

    const { error: trackingError } = await supabase
      .from('migrations')
      .insert({
        id: '003_tool_versioning_schema',
        executed_at: new Date().toISOString()
      });

    if (trackingError && !trackingError.message.includes('duplicate key')) {
      console.warn('⚠️  Could not record migration:', trackingError.message);
    } else {
      console.log('✅ Migration recorded successfully');
    }

    // Step 6: Display summary
    console.log('\n🎉 Tool Versioning System Migration Completed Successfully!');
    console.log('\n📊 Migration Summary:');
    console.log('   ✅ 3 new tables created (tool_versions, version_audit_log, version_comparisons)');
    console.log('   ✅ 2 new columns added to tools table (current_version_id, version_count)');
    console.log('   ✅ Rollback functions installed');
    console.log('   ✅ Triggers and indexes created');
    console.log('   ✅ Tool version initialization completed');
    console.log('\n🔧 Next Steps:');
    console.log('   1. Update your application code to use the new versioning system');
    console.log('   2. Test version creation and rollback functionality');
    console.log('   3. Configure version cleanup policies if needed');
    console.log('\n📚 Documentation:');
    console.log('   - API endpoints: /api/admin/tools/[id]/versions/*');
    console.log('   - Components: src/components/admin/versioning/*');
    console.log('   - Types: src/lib/types/versioning.ts');

  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    console.error('\n🔄 Rollback Instructions:');
    console.error('   1. Drop the new tables if they were created:');
    console.error('      DROP TABLE IF EXISTS version_comparisons CASCADE;');
    console.error('      DROP TABLE IF EXISTS version_audit_log CASCADE;');
    console.error('      DROP TABLE IF EXISTS tool_versions CASCADE;');
    console.error('   2. Remove added columns from tools table:');
    console.error('      ALTER TABLE tools DROP COLUMN IF EXISTS current_version_id;');
    console.error('      ALTER TABLE tools DROP COLUMN IF EXISTS version_count;');
    console.error('   3. Drop functions:');
    console.error('      DROP FUNCTION IF EXISTS rollback_tool_to_version CASCADE;');
    console.error('      DROP FUNCTION IF EXISTS initialize_tool_versions CASCADE;');
    
    process.exit(1);
  }
}

// Helper function to execute SQL (manual execution required)
async function executeSQLDirect(sql: string) {
  console.log('⚠️  Manual SQL execution required:');
  console.log(sql);
  console.log('Please run this SQL in your Supabase SQL editor and press Enter to continue...');
  await new Promise(resolve => {
    process.stdin.once('data', () => resolve(undefined));
  });
}

// Run the migration
if (require.main === module) {
  runVersioningMigration()
    .then(() => {
      console.log('\n✨ Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration failed:', error);
      process.exit(1);
    });
}

export { runVersioningMigration };
