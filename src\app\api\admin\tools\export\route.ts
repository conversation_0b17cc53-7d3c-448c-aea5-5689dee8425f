import { NextRequest, NextResponse } from 'next/server';
import { getAdminTools } from '../../../../../lib/supabase';
import { validateApiKey } from '../../../../../lib/auth';
import { AdminToolFilters } from '../../../../../lib/types';

/**
 * GET /api/admin/tools/export
 * Export tool data in CSV or JSON format with filtering support
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'json';
    const category = searchParams.get('category') || undefined;
    const status = searchParams.get('status') || undefined;
    const verified = searchParams.get('verified') || undefined;
    const dateFrom = searchParams.get('dateFrom') || undefined;
    const dateTo = searchParams.get('dateTo') || undefined;
    const includeFields = searchParams.get('fields')?.split(',') || undefined;

    // Validate format
    if (!['json', 'csv'].includes(format)) {
      return NextResponse.json(
        { success: false, error: 'Invalid format. Use "json" or "csv"' },
        { status: 400 }
      );
    }

    // Build filters
    const filters: AdminToolFilters = {};
    if (category) filters.category = category;
    if (status) filters.contentStatus = status as any;
    if (verified !== undefined) filters.verified = verified === 'true';
    if (dateFrom) filters.createdAfter = dateFrom;
    if (dateTo) filters.createdBefore = dateTo;

    // Fetch tools data
    const { data: tools } = await getAdminTools(filters);

    if (format === 'json') {
      return exportAsJSON(tools as any[], includeFields);
    } else {
      return exportAsCSV(tools as any[], includeFields);
    }

  } catch (error) {
    console.error('Tool export error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to export tools' },
      { status: 500 }
    );
  }
}

/**
 * Export tools as JSON
 */
function exportAsJSON(tools: any[], includeFields?: string[]) {
  let exportData = tools;

  // Filter fields if specified
  if (includeFields && includeFields.length > 0) {
    exportData = tools.map(tool => {
      const filtered: any = {};
      includeFields.forEach(field => {
        if (tool.hasOwnProperty(field)) {
          filtered[field] = tool[field];
        }
      });
      return filtered;
    });
  }

  const jsonContent = JSON.stringify({
    exportedAt: new Date().toISOString(),
    totalTools: tools.length,
    tools: exportData
  }, null, 2);

  const filename = `tools-export-${new Date().toISOString().split('T')[0]}.json`;

  return new NextResponse(jsonContent, {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Content-Disposition': `attachment; filename="${filename}"`
    }
  });
}

/**
 * Export tools as CSV
 */
function exportAsCSV(tools: any[], includeFields?: string[]) {
  if (tools.length === 0) {
    return new NextResponse('No tools to export', {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="tools-export-empty.csv"'
      }
    });
  }

  // Define default fields for CSV export
  const defaultFields = [
    'id', 'name', 'slug', 'description', 'short_description', 
    'link', 'website', 'category_id', 'subcategory', 'company',
    'is_verified', 'is_claimed', 'content_status', 'created_at', 'updated_at'
  ];

  const fieldsToExport = includeFields || defaultFields;
  
  // Create CSV headers
  const headers = fieldsToExport.map(field => {
    // Convert snake_case to Title Case for headers
    return field.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  });

  // Create CSV rows
  const csvRows = tools.map(tool => {
    return fieldsToExport.map(field => {
      let value = tool[field];
      
      // Handle different data types
      if (value === null || value === undefined) {
        return '';
      }
      
      if (typeof value === 'object') {
        value = JSON.stringify(value);
      }
      
      if (typeof value === 'string') {
        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          value = `"${value.replace(/"/g, '""')}"`;
        }
      }
      
      return value;
    });
  });

  // Combine headers and rows
  const csvContent = [
    headers.join(','),
    ...csvRows.map(row => row.join(','))
  ].join('\n');

  const filename = `tools-export-${new Date().toISOString().split('T')[0]}.csv`;

  return new NextResponse(csvContent, {
    status: 200,
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`
    }
  });
}
