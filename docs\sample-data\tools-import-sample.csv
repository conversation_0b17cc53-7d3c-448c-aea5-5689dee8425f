Name,Slug,Description,Short Description,Link,Website,Category Id,Subcategory,Company,Is Verified,Is Claimed,Content Status,Features,Pricing
"Sample AI Tool 1","sample-ai-tool-1","A comprehensive AI tool for content generation and analysis","AI content generation tool","/tools/sample-ai-tool-1","https://sampleaitool1.com","ai-tools","content-generation","Sample AI Company",true,false,"published","{""key_features"":[""Advanced text generation"",""Multi-language support"",""API integration"",""Real-time collaboration""]}","{""type"":""freemium"",""free_tier"":{""description"":""Basic features with limited usage""}}"
"Sample AI Tool 2","sample-ai-tool-2","Advanced image generation and editing AI platform","AI image generation platform","/tools/sample-ai-tool-2","https://sampleaitool2.com","ai-tools","image-generation","Visual AI Corp",true,true,"published","{""key_features"":[""Text-to-image generation"",""Image editing tools"",""Style transfer"",""Batch processing""]}","{""type"":""subscription""}"
"Sample AI Tool 3","sample-ai-tool-3","AI-powered data analysis and visualization platform","AI data analysis tool","/tools/sample-ai-tool-3","https://sampleaitool3.com","ai-tools","data-analysis","DataAI Solutions",false,false,"draft","{""key_features"":[""Automated data processing"",""Predictive modeling"",""Interactive dashboards"",""Real-time analytics""]}","{""type"":""enterprise"",""contact_required"":true}"
"Sample AI Tool 4","sample-ai-tool-4","Voice synthesis and speech recognition AI","AI voice technology","/tools/sample-ai-tool-4","https://sampleaitool4.com","ai-tools","voice-ai","VoiceAI Inc",true,false,"published","{""key_features"":[""Natural voice synthesis"",""Multi-language support"",""Real-time processing"",""Custom voice training""]}","{""type"":""pay-per-use"",""starting_price"":""$0.01 per minute""}"
"Sample AI Tool 5","sample-ai-tool-5","AI-powered code generation and review platform","AI coding assistant","/tools/sample-ai-tool-5","https://sampleaitool5.com","ai-tools","development","CodeAI Labs",false,true,"published","{""key_features"":[""Code generation"",""Bug detection"",""Code review"",""Multiple programming languages""]}","{""type"":""free"",""open_source"":true}"
