'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { apiClient } from '@/lib/api';
import { AITool } from '@/lib/types';
import { EditToolForm } from '@/components/admin/EditToolForm';

interface EditToolPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditToolPage({ params }: EditToolPageProps) {
  const router = useRouter();
  const [tool, setTool] = useState<AITool | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    const loadTool = async () => {
      try {
        setLoading(true);
        const adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';

        // Await params to get the id
        const resolvedParams = await params;

        // Get the specific tool to edit
        const foundTool = await apiClient.getAdminToolById(resolvedParams.id, adminApiKey);

        if (!foundTool) {
          setError('Tool not found');
          return;
        }

        // Validate tool data structure
        if (typeof foundTool !== 'object' || !foundTool.id) {
          console.error('Invalid tool data received:', foundTool);
          setError('Invalid tool data received from server');
          return;
        }

        setTool(foundTool);

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load tool');
      } finally {
        setLoading(false);
      }
    };

    loadTool();
  }, [params]);

  const handleSuccess = (toolId: string) => {
    setIsRedirecting(true);
    // Redirect back to admin panel after successful update
    router.push('/admin');
  };

  const handleCancel = () => {
    router.push('/admin');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading tool...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-white mb-2">Error</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <button
            onClick={() => router.push('/admin')}
            className="bg-orange-500 hover:bg-orange-600 px-6 py-2 rounded-lg text-white font-medium"
          >
            Back to Admin
          </button>
        </div>
      </div>
    );
  }

  if (isRedirecting) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Redirecting to admin panel...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-zinc-900 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-gray-400 mb-4">
            <span>Admin</span>
            <span>›</span>
            <span>Tools</span>
            <span>›</span>
            <span className="text-white">Edit Tool</span>
          </div>

          <h1 className="text-3xl font-bold text-white mb-2">
            Edit AI Tool: {tool?.name}
          </h1>
          <p className="text-gray-300">
            Update the tool information and content. All changes will be saved to the database.
          </p>
        </div>

        {/* Edit Tool Form */}
        {tool && (
          <EditToolForm
            tool={tool}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        )}
      </div>
    </div>
  );
}
