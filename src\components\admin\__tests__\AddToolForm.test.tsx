import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock modules before importing components
jest.mock('@/lib/api', () => ({
  apiClient: {
    getCategories: jest.fn(),
    createAdminTool: jest.fn(),
    updateAdminTool: jest.fn(),
  }
}));

jest.mock('@/lib/validation/tool-uniqueness', () => ({
  checkToolUniqueness: jest.fn(),
  generateUniqueSlug: jest.fn(),
}));

jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null })),
        })),
      })),
    })),
  },
}));

import { AddToolForm } from '../AddToolForm';
import { apiClient } from '@/lib/api';
import * as uniquenessValidation from '@/lib/validation/tool-uniqueness';

// Get mocked functions
const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Mock uniqueness validation
jest.mock('@/lib/validation/tool-uniqueness');
const mockUniquenessValidation = uniquenessValidation as jest.Mocked<typeof uniquenessValidation>;

// Mock categories
const mockCategories = [
  { id: 'ai-writing', title: 'AI Writing' },
  { id: 'ai-image', title: 'AI Image Generation' },
];

describe('AddToolForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockApiClient.getCategories.mockResolvedValue(mockCategories);
    mockApiClient.createAdminTool.mockResolvedValue({
      id: 'test-tool-123',
      name: 'Test Tool',
      slug: 'test-tool',
      description: 'Test description',
      shortDescription: 'Short test description',
      detailedDescription: 'Detailed test description',
      logoUrl: '',
      link: '/tools/test-tool',
      website: 'https://test-tool.com',
      category: 'ai-writing',
      subcategory: '',
      company: 'Test Company',
      isVerified: false,
      isClaimed: false,
      contentStatus: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    // Setup uniqueness validation mocks
    mockUniquenessValidation.checkToolUniqueness.mockResolvedValue({
      isValid: true,
      errors: {},
      conflictingTools: []
    });
    mockUniquenessValidation.generateUniqueSlug.mockResolvedValue('test-tool');
  });

  it('renders the form with all required fields', async () => {
    render(<AddToolForm />);

    await waitFor(() => {
      expect(screen.getByText('Add New AI Tool')).toBeInTheDocument();
    });

    // Check for basic information fields
    expect(screen.getByLabelText(/tool name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/tool url/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/category/i)).toBeInTheDocument();
  });

  it('auto-generates slug and link from tool name', async () => {
    const user = userEvent.setup();
    render(<AddToolForm />);

    await waitFor(() => {
      expect(screen.getByText('Add New AI Tool')).toBeInTheDocument();
    });

    // The form uses collapsible sections, so we need to find the name field in the visible sections
    // Look for the name field that should be visible by default
    const nameInput = screen.getByLabelText(/tool name/i);
    await user.type(nameInput, 'Test AI Tool');

    // Check that slug is auto-generated in the URL slug field
    await waitFor(() => {
      const linkInput = screen.getByDisplayValue('/tools/test-ai-tool');
      expect(linkInput).toBeInTheDocument();
    });
  });

  it('validates required fields', async () => {
    const user = userEvent.setup();
    render(<AddToolForm />);

    await waitFor(() => {
      expect(screen.getByText('Add New AI Tool')).toBeInTheDocument();
    });

    // Expand the Basic Information section to access required fields
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    // Try to submit without filling required fields
    const submitButton = screen.getByRole('button', { name: /create tool/i });
    await user.click(submitButton);

    // Check for validation errors
    await waitFor(() => {
      expect(screen.getByText(/tool name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/please enter a valid website url/i)).toBeInTheDocument();
      expect(screen.getByText(/description must be at least 10 characters/i)).toBeInTheDocument();
      expect(screen.getByText(/category is required/i)).toBeInTheDocument();
    });
  });

  it('handles short_description field correctly', async () => {
    const user = userEvent.setup();
    render(<AddToolForm />);

    await waitFor(() => {
      expect(screen.getByText('Add New AI Tool')).toBeInTheDocument();
    });

    // Expand content section to access short_description
    const contentButton = screen.getByText('Content & Features');
    await user.click(contentButton);

    // Wait for the section to expand and check that short_description field exists
    await waitFor(() => {
      const shortDescInput = screen.getByLabelText(/short description/i);
      expect(shortDescInput).toBeInTheDocument();
    });

    const shortDescInput = screen.getByLabelText(/short description/i);

    // Test character limit
    await user.type(shortDescInput, 'A'.repeat(151)); // Exceed 150 char limit

    await waitFor(() => {
      expect(screen.getByText(/short description must be less than 150 characters/i)).toBeInTheDocument();
    });
  });

  it('validates data transformation between camelCase and snake_case', async () => {
    const user = userEvent.setup();
    render(<AddToolForm />);

    await waitFor(() => {
      expect(screen.getByText('Add New AI Tool')).toBeInTheDocument();
    });

    // Fill out form with all description fields
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    // Wait for section to expand before interacting with fields
    await waitFor(() => {
      expect(screen.getByLabelText(/tool name/i)).toBeInTheDocument();
    });

    await user.type(screen.getByLabelText(/tool name/i), 'Test AI Tool');
    await user.type(screen.getByLabelText(/tool url/i), 'https://test-tool.com');
    await user.type(screen.getByLabelText(/description/i), 'This is a test description for the AI tool');

    // Select category (this is in the always-visible Categorization section)
    const categorySelect = screen.getByLabelText(/category/i);
    await user.selectOptions(categorySelect, 'ai-writing');

    // Fill out content section
    const contentButton = screen.getByText('Content & Features');
    await user.click(contentButton);

    // Wait for content section to expand
    await waitFor(() => {
      expect(screen.getByLabelText(/short description/i)).toBeInTheDocument();
    });

    await user.type(screen.getByLabelText(/short description/i), 'Short test description');
    await user.type(screen.getByLabelText(/detailed description/i), 'This is a detailed description of the test AI tool');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /create tool/i });
    await user.click(submitButton);

    // Verify API call with correct data transformation
    await waitFor(() => {
      expect(mockApiClient.createAdminTool).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Test AI Tool',
          slug: 'test-ai-tool',
          description: 'This is a test description for the AI tool',
          shortDescription: 'Short test description',
          detailedDescription: 'This is a detailed description of the test AI tool',
          link: '/tools/test-ai-tool',
          website: 'https://test-tool.com',
          category: 'ai-writing', // Should be transformed from category_id
        }),
        expect.any(String)
      );
    });
  });

  it('handles uniqueness validation errors', async () => {
    const user = userEvent.setup();

    // Mock uniqueness validation to return conflicts
    mockUniquenessValidation.checkToolUniqueness.mockResolvedValue({
      isValid: false,
      errors: {
        name: 'A tool named "Existing Tool" already exists. Please choose a different name.',
        website: 'The website "https://existing.com" is already associated with "Existing Tool".'
      },
      conflictingTools: [
        {
          field: 'name',
          tool: { id: 'existing-1', name: 'Existing Tool', slug: 'existing-tool', website: 'https://existing.com' }
        }
      ]
    });

    render(<AddToolForm />);

    await waitFor(() => {
      expect(screen.getByText('Add New AI Tool')).toBeInTheDocument();
    });

    // Fill out form with conflicting data
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    // Wait for section to expand
    await waitFor(() => {
      expect(screen.getByLabelText(/tool name/i)).toBeInTheDocument();
    });

    await user.type(screen.getByLabelText(/tool name/i), 'Existing Tool');
    await user.type(screen.getByLabelText(/tool url/i), 'https://existing.com');
    await user.type(screen.getByLabelText(/description/i), 'This is a test description');

    const categorySelect = screen.getByLabelText(/category/i);
    await user.selectOptions(categorySelect, 'ai-writing');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /create tool/i });
    await user.click(submitButton);

    // Check for uniqueness validation errors
    await waitFor(() => {
      expect(screen.getByText(/a tool named "existing tool" already exists/i)).toBeInTheDocument();
      expect(screen.getByText(/the website "https:\/\/existing\.com" is already associated/i)).toBeInTheDocument();
    });

    // Verify API was not called due to validation failure
    expect(mockApiClient.createAdminTool).not.toHaveBeenCalled();
  });

  it('shows success message after successful submission', async () => {
    const user = userEvent.setup();
    render(<AddToolForm />);

    await waitFor(() => {
      expect(screen.getByText('Add New AI Tool')).toBeInTheDocument();
    });

    // Fill out minimum required fields
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    // Wait for section to expand
    await waitFor(() => {
      expect(screen.getByLabelText(/tool name/i)).toBeInTheDocument();
    });

    await user.type(screen.getByLabelText(/tool name/i), 'Test Tool');
    await user.type(screen.getByLabelText(/tool url/i), 'https://test.com');
    await user.type(screen.getByLabelText(/description/i), 'Test description');

    const categorySelect = screen.getByLabelText(/category/i);
    await user.selectOptions(categorySelect, 'ai-writing');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /create tool/i });
    await user.click(submitButton);

    // Check for success message
    await waitFor(() => {
      expect(screen.getByText('Tool Created Successfully!')).toBeInTheDocument();
    });
  });

  it('handles API errors gracefully', async () => {
    const user = userEvent.setup();
    mockApiClient.createAdminTool.mockRejectedValue(new Error('API Error'));

    render(<AddToolForm />);

    await waitFor(() => {
      expect(screen.getByText('Add New AI Tool')).toBeInTheDocument();
    });

    // Fill out minimum required fields
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    await user.type(screen.getByLabelText(/tool name/i), 'Test Tool');
    await user.type(screen.getByLabelText(/tool url/i), 'https://test.com');
    await user.type(screen.getByLabelText(/description/i), 'Test description');
    
    const categorySelect = screen.getByLabelText(/category/i);
    await user.selectOptions(categorySelect, 'ai-writing');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /create tool/i });
    await user.click(submitButton);

    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(/form submission failed/i)).toBeInTheDocument();
      expect(screen.getByText('API Error')).toBeInTheDocument();
    });
  });

  it('validates features field parsing', async () => {
    const user = userEvent.setup();
    render(<AddToolForm />);

    await waitFor(() => {
      expect(screen.getByText('Add New AI Tool')).toBeInTheDocument();
    });

    // Fill out basic fields
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    await user.type(screen.getByLabelText(/tool name/i), 'Test Tool');
    await user.type(screen.getByLabelText(/tool url/i), 'https://test.com');
    await user.type(screen.getByLabelText(/description/i), 'Test description');
    
    const categorySelect = screen.getByLabelText(/category/i);
    await user.selectOptions(categorySelect, 'ai-writing');

    // Fill out features
    const contentButton = screen.getByText('Content & Features');
    await user.click(contentButton);

    const featuresInput = screen.getByLabelText(/features/i);
    await user.type(featuresInput, 'Feature 1\nFeature 2\nFeature 3');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /create tool/i });
    await user.click(submitButton);

    // Verify features are parsed correctly
    await waitFor(() => {
      expect(mockApiClient.createAdminTool).toHaveBeenCalledWith(
        expect.objectContaining({
          features: ['Feature 1', 'Feature 2', 'Feature 3']
        }),
        expect.any(String)
      );
    });
  });

  it('handles JSONB field validation correctly', async () => {
    const user = userEvent.setup();
    render(<AddToolForm />);

    await waitFor(() => {
      expect(screen.getByText('Add New AI Tool')).toBeInTheDocument();
    });

    // Fill out basic fields
    const basicInfoButton = screen.getByText('Basic Information');
    await user.click(basicInfoButton);

    await user.type(screen.getByLabelText(/tool name/i), 'Test Tool');
    await user.type(screen.getByLabelText(/tool url/i), 'https://test.com');
    await user.type(screen.getByLabelText(/description/i), 'Test description');

    const categorySelect = screen.getByLabelText(/category/i);
    await user.selectOptions(categorySelect, 'ai-writing');

    // Test pricing section
    const pricingButton = screen.getByText('Pricing Information');
    await user.click(pricingButton);

    const pricingSelect = screen.getByLabelText(/pricing type/i);
    await user.selectOptions(pricingSelect, 'freemium');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /create tool/i });
    await user.click(submitButton);

    // Verify JSONB fields are handled correctly
    await waitFor(() => {
      expect(mockApiClient.createAdminTool).toHaveBeenCalledWith(
        expect.objectContaining({
          pricing: expect.objectContaining({
            type: 'freemium'
          })
        }),
        expect.any(String)
      );
    });
  });
});
