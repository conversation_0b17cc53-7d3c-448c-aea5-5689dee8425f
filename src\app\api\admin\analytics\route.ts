import { NextRequest, NextResponse } from 'next/server';
import { validateApi<PERSON><PERSON> } from '@/lib/auth';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/admin/analytics
 * Get analytics data for the admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '7d';

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (range) {
      case '1d':
        startDate.setDate(endDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    // Get total tools count
    const { count: totalTools } = await supabase
      .from('tools')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'published');

    // Get tools data for analysis
    const { data: toolsData, error: toolsError } = await supabase
      .from('tools')
      .select('id, name, category, created_at, updated_at')
      .eq('status', 'published')
      .order('created_at', { ascending: false });

    if (toolsError) {
      console.error('Error fetching tools data:', toolsError);
    }

    // Generate mock analytics data based on real tools
    const mockAnalytics = {
      overview: {
        totalTools: totalTools || 0,
        totalViews: Math.floor(Math.random() * 50000) + 20000,
        totalUsers: Math.floor(Math.random() * 10000) + 5000,
        avgRating: 4.0 + Math.random() * 0.8
      },
      traffic: generateTrafficData(startDate, endDate),
      topTools: generateTopToolsData(toolsData || []),
      categories: generateCategoryData(toolsData || []),
      performance: {
        avgLoadTime: 1.0 + Math.random() * 0.5,
        bounceRate: 0.25 + Math.random() * 0.15,
        conversionRate: 0.05 + Math.random() * 0.05,
        errorRate: Math.random() * 0.03
      }
    };

    return NextResponse.json({
      success: true,
      data: mockAnalytics
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function generateTrafficData(startDate: Date, endDate: Date) {
  const traffic = [];
  const currentDate = new Date(startDate);
  
  while (currentDate <= endDate) {
    const baseViews = 1000 + Math.floor(Math.random() * 500);
    const baseUsers = Math.floor(baseViews * (0.6 + Math.random() * 0.3));
    
    traffic.push({
      date: currentDate.toISOString().split('T')[0],
      views: baseViews,
      users: baseUsers
    });
    
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return traffic;
}

function generateTopToolsData(tools: any[]) {
  const shuffled = [...tools].sort(() => 0.5 - Math.random());
  const topTools = shuffled.slice(0, 5);
  
  return topTools.map((tool, index) => ({
    id: tool.id,
    name: tool.name,
    views: Math.floor(Math.random() * 2000) + 1000 - (index * 200),
    rating: 4.0 + Math.random() * 0.8,
    category: tool.category || 'AI Tools'
  }));
}

function generateCategoryData(tools: any[]) {
  const categoryMap = new Map();
  
  tools.forEach(tool => {
    const category = tool.category || 'AI Tools';
    if (!categoryMap.has(category)) {
      categoryMap.set(category, { count: 0, views: 0 });
    }
    
    const data = categoryMap.get(category);
    data.count += 1;
    data.views += Math.floor(Math.random() * 1000) + 500;
  });
  
  return Array.from(categoryMap.entries()).map(([name, data]) => ({
    name,
    count: data.count,
    views: data.views
  })).sort((a, b) => b.views - a.views);
}
