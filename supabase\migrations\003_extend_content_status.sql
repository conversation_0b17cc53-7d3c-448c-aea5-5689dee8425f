-- Migration: Extend content_status to support editorial workflow states
-- Date: 2024-12-17
-- Description: Add support for under_review, approved, and rejected status values

-- Drop the existing check constraint
ALTER TABLE tools DROP CONSTRAINT IF EXISTS tools_content_status_check;

-- Add the new check constraint with extended values
ALTER TABLE tools ADD CONSTRAINT tools_content_status_check 
  CHECK (content_status IN ('draft', 'published', 'archived', 'under_review', 'approved', 'rejected'));

-- Update the RLS policy to include approved tools as viewable
DROP POLICY IF EXISTS "Published tools are viewable by everyone" ON tools;
CREATE POLICY "Published and approved tools are viewable by everyone" 
  ON tools FOR SELECT 
  USING (content_status IN ('published', 'approved'));

-- Add index for better performance on new status values
CREATE INDEX IF NOT EXISTS idx_tools_content_status_workflow ON tools(content_status) 
  WHERE content_status IN ('under_review', 'approved', 'rejected');

-- Add comment for documentation
COMMENT ON COLUMN tools.content_status IS 'Content status supporting editorial workflow: draft, under_review, approved, published, rejected, archived';
