import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { supabase } from '@/lib/supabase';
import { generateContent } from '@/lib/ai';

/**
 * POST /api/admin/prompts/test
 * Test a prompt template with sample data
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { promptId, testData } = body;

    if (!promptId) {
      return NextResponse.json(
        { success: false, error: 'Prompt ID is required' },
        { status: 400 }
      );
    }

    // Get the prompt template from database
    const { data: promptRecord, error: fetchError } = await supabase
      .from('system_configuration')
      .select('config_value')
      .eq('id', promptId)
      .eq('config_type', 'prompt_template')
      .eq('is_active', true)
      .single();

    if (fetchError || !promptRecord) {
      return NextResponse.json(
        { success: false, error: 'Prompt template not found' },
        { status: 404 }
      );
    }

    const promptTemplate = promptRecord.config_value;

    // Use sample test data if not provided
    const sampleData = testData || {
      toolName: 'Sample AI Tool',
      toolUrl: 'https://example.com/sample-tool',
      scrapedContent: `# Sample AI Tool

This is a sample AI tool for testing prompt templates. It provides various AI-powered features including:

- Natural language processing
- Content generation
- Data analysis
- Automated workflows

## Features
- Easy to use interface
- Fast processing
- High accuracy
- Multiple integrations

## Pricing
- Free tier: Basic features
- Pro tier: $29/month - Advanced features
- Enterprise: Custom pricing

The tool is designed for businesses and individuals who need AI assistance.`
    };

    // Replace variables in the template
    let processedPrompt = promptTemplate.template;
    
    // Replace common variables
    const variableMap: Record<string, string> = {
      toolName: sampleData.toolName,
      toolUrl: sampleData.toolUrl,
      scrapedContent: sampleData.scrapedContent,
      url: sampleData.toolUrl
    };

    // Replace all variables in the template
    for (const [variable, value] of Object.entries(variableMap)) {
      const regex = new RegExp(`\\{${variable}\\}`, 'g');
      processedPrompt = processedPrompt.replace(regex, value);
    }

    // Test the prompt with AI
    try {
      const startTime = Date.now();
      
      const aiResponse = await generateContent({
        prompt: processedPrompt,
        provider: 'openai', // Use OpenAI for testing
        options: {
          maxTokens: 1000,
          temperature: 0.7
        }
      });

      const responseTime = Date.now() - startTime;

      // Update usage count
      const updatedUsage = (promptTemplate.usage || 0) + 1;
      await supabase
        .from('system_configuration')
        .update({
          config_value: {
            ...promptTemplate,
            usage: updatedUsage
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', promptId);

      return NextResponse.json({
        success: true,
        data: {
          promptId,
          processedPrompt,
          aiResponse: aiResponse.content,
          responseTime,
          testData: sampleData,
          metadata: {
            provider: 'openai',
            model: aiResponse.model || 'gpt-4o-2024-11-20',
            tokensUsed: aiResponse.tokensUsed || 0,
            cost: aiResponse.cost || 0
          }
        }
      });

    } catch (aiError) {
      console.error('AI generation error during prompt test:', aiError);
      
      return NextResponse.json({
        success: false,
        error: 'AI generation failed',
        details: aiError instanceof Error ? aiError.message : 'Unknown AI error',
        data: {
          promptId,
          processedPrompt,
          testData: sampleData
        }
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Prompt test API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/prompts/test
 * Get sample test data for prompt testing
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const sampleTestData = {
      toolName: 'ChatGPT Alternative Pro',
      toolUrl: 'https://example.com/chatgpt-alternative',
      scrapedContent: `# ChatGPT Alternative Pro

A powerful AI assistant that provides advanced conversational AI capabilities with enhanced features for professional use.

## Key Features
- Advanced natural language understanding
- Multi-language support (50+ languages)
- Custom personality settings
- Integration with popular tools
- Real-time collaboration
- Advanced analytics and insights

## Use Cases
- Customer support automation
- Content creation and editing
- Research and analysis
- Code generation and debugging
- Educational assistance
- Creative writing

## Pricing
- Starter: Free - Basic chat features, 100 messages/month
- Professional: $19/month - Unlimited messages, advanced features
- Team: $49/month - Team collaboration, admin controls
- Enterprise: Custom pricing - Full customization, dedicated support

## Technical Specifications
- API access available
- 99.9% uptime guarantee
- SOC 2 Type II certified
- GDPR compliant
- 24/7 customer support

The platform is designed for businesses and professionals who need reliable AI assistance.`
    };

    return NextResponse.json({
      success: true,
      data: sampleTestData
    });

  } catch (error) {
    console.error('Prompt test sample data API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
