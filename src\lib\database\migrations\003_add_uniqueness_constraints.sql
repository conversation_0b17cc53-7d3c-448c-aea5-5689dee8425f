-- Migration 003: Add Uniqueness Constraints for Tool Names and Slugs
-- This migration adds database-level unique constraints to prevent duplicate tools

-- =====================================================
-- ENABLE REQUIRED EXTENSIONS
-- =====================================================

-- Enable pg_trgm extension for similarity() function used in duplicate detection
-- This extension provides trigram matching for fuzzy text similarity
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- =====================================================
-- UNIQUENESS CONSTRAINTS
-- =====================================================

-- Add unique constraint for tool names (case-insensitive)
-- This prevents duplicate tool names regardless of case
CREATE UNIQUE INDEX IF NOT EXISTS idx_tools_name_unique 
ON tools (LOWER(name));

-- Add unique constraint for tool slugs (already exists but ensure it's there)
-- This is critical for URL routing
CREATE UNIQUE INDEX IF NOT EXISTS idx_tools_slug_unique 
ON tools (slug);

-- Add unique constraint for tool websites (case-insensitive)
-- This prevents the same website from being added multiple times
CREATE UNIQUE INDEX IF NOT EXISTS idx_tools_website_unique 
ON tools (LOWER(website)) 
WHERE website IS NOT NULL AND website != '';

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Add indexes for common search patterns
CREATE INDEX IF NOT EXISTS idx_tools_name_search 
ON tools USING gin(to_tsvector('english', name));

CREATE INDEX IF NOT EXISTS idx_tools_description_search 
ON tools USING gin(to_tsvector('english', description));

CREATE INDEX IF NOT EXISTS idx_tools_company_search 
ON tools (LOWER(company)) 
WHERE company IS NOT NULL AND company != '';

-- Add composite index for category and status filtering
CREATE INDEX IF NOT EXISTS idx_tools_category_status 
ON tools (category_id, content_status);

-- Add index for verification status
CREATE INDEX IF NOT EXISTS idx_tools_verification 
ON tools (is_verified, content_status);

-- =====================================================
-- VALIDATION FUNCTIONS
-- =====================================================

-- Function to check for duplicate tool names
CREATE OR REPLACE FUNCTION check_duplicate_tool_name(tool_name TEXT, exclude_id TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM tools 
    WHERE LOWER(name) = LOWER(tool_name) 
    AND (exclude_id IS NULL OR id != exclude_id)
  );
END;
$$ LANGUAGE plpgsql;

-- Function to check for duplicate tool slugs
CREATE OR REPLACE FUNCTION check_duplicate_tool_slug(tool_slug TEXT, exclude_id TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM tools 
    WHERE slug = tool_slug 
    AND (exclude_id IS NULL OR id != exclude_id)
  );
END;
$$ LANGUAGE plpgsql;

-- Function to check for duplicate tool websites
CREATE OR REPLACE FUNCTION check_duplicate_tool_website(tool_website TEXT, exclude_id TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM tools 
    WHERE LOWER(website) = LOWER(tool_website) 
    AND website IS NOT NULL 
    AND website != ''
    AND (exclude_id IS NULL OR id != exclude_id)
  );
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique slug from name
CREATE OR REPLACE FUNCTION generate_unique_slug(tool_name TEXT, exclude_id TEXT DEFAULT NULL)
RETURNS TEXT AS $$
DECLARE
  base_slug TEXT;
  final_slug TEXT;
  counter INTEGER := 1;
BEGIN
  -- Generate base slug from name
  base_slug := LOWER(REGEXP_REPLACE(tool_name, '[^a-zA-Z0-9]+', '-', 'g'));
  base_slug := TRIM(BOTH '-' FROM base_slug);
  
  -- Start with base slug
  final_slug := base_slug;
  
  -- Check if slug exists and increment counter if needed
  WHILE check_duplicate_tool_slug(final_slug, exclude_id) LOOP
    final_slug := base_slug || '-' || counter;
    counter := counter + 1;
    
    -- Safety check to prevent infinite loop
    IF counter > 1000 THEN
      RAISE EXCEPTION 'Unable to generate unique slug after 1000 attempts';
    END IF;
  END LOOP;
  
  RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS FOR AUTOMATIC SLUG GENERATION
-- =====================================================

-- Function to automatically generate slug if not provided
CREATE OR REPLACE FUNCTION auto_generate_slug()
RETURNS TRIGGER AS $$
BEGIN
  -- Only generate slug if it's empty or null
  IF NEW.slug IS NULL OR NEW.slug = '' THEN
    NEW.slug := generate_unique_slug(NEW.name, NEW.id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate slug on insert
DROP TRIGGER IF EXISTS trigger_auto_generate_slug ON tools;
CREATE TRIGGER trigger_auto_generate_slug
  BEFORE INSERT ON tools
  FOR EACH ROW
  EXECUTE FUNCTION auto_generate_slug();

-- =====================================================
-- DATA CLEANUP (HANDLE EXISTING DUPLICATES)
-- =====================================================

-- Update any tools with empty slugs
UPDATE tools 
SET slug = generate_unique_slug(name, id)
WHERE slug IS NULL OR slug = '';

-- Find and report potential duplicates (for manual review)
-- This query will show tools that might be duplicates based on name similarity
-- Note: Uses similarity() function from pg_trgm extension (enabled above)
CREATE OR REPLACE VIEW potential_duplicate_tools AS
SELECT 
  t1.id as tool1_id,
  t1.name as tool1_name,
  t1.slug as tool1_slug,
  t2.id as tool2_id,
  t2.name as tool2_name,
  t2.slug as tool2_slug,
  similarity(LOWER(t1.name), LOWER(t2.name)) as name_similarity
FROM tools t1
JOIN tools t2 ON t1.id < t2.id
WHERE similarity(LOWER(t1.name), LOWER(t2.name)) > 0.8
ORDER BY name_similarity DESC;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON INDEX idx_tools_name_unique IS 'Ensures tool names are unique (case-insensitive)';
COMMENT ON INDEX idx_tools_slug_unique IS 'Ensures tool slugs are unique for URL routing';
COMMENT ON INDEX idx_tools_website_unique IS 'Prevents duplicate websites (case-insensitive)';
COMMENT ON FUNCTION check_duplicate_tool_name IS 'Checks if a tool name already exists';
COMMENT ON FUNCTION check_duplicate_tool_slug IS 'Checks if a tool slug already exists';
COMMENT ON FUNCTION check_duplicate_tool_website IS 'Checks if a tool website already exists';
COMMENT ON FUNCTION generate_unique_slug IS 'Generates a unique slug from tool name';
COMMENT ON VIEW potential_duplicate_tools IS 'Shows potential duplicate tools for manual review using pg_trgm similarity matching';
