/**
 * Centralized Logging System
 * 
 * Provides structured logging with different levels and contexts.
 * Replaces console.log statements throughout the application.
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogContext {
  component?: string;
  operation?: string;
  userId?: string;
  toolId?: string;
  jobId?: string;
  requestId?: string;
  [key: string]: any;
}

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  context?: LogContext;
  error?: Error;
  metadata?: Record<string, any>;
}

class Logger {
  private logLevel: LogLevel;
  private isDevelopment: boolean;

  constructor() {
    this.logLevel = this.getLogLevelFromEnv();
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  private getLogLevelFromEnv(): LogLevel {
    const envLevel = process.env.LOG_LEVEL?.toUpperCase();
    switch (envLevel) {
      case 'DEBUG': return LogLevel.DEBUG;
      case 'INFO': return LogLevel.INFO;
      case 'WARN': return LogLevel.WARN;
      case 'ERROR': return LogLevel.ERROR;
      default: return this.isDevelopment ? LogLevel.DEBUG : LogLevel.INFO;
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private formatMessage(level: LogLevel, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const levelName = LogLevel[level];
    
    let formatted = `[${timestamp}] ${levelName}: ${message}`;
    
    if (context) {
      const contextStr = Object.entries(context)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => `${key}=${value}`)
        .join(' ');
      
      if (contextStr) {
        formatted += ` | ${contextStr}`;
      }
    }
    
    return formatted;
  }

  private log(level: LogLevel, message: string, context?: LogContext, error?: Error, metadata?: Record<string, any>): void {
    if (!this.shouldLog(level)) return;

    const logEntry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      context,
      error,
      metadata
    };

    const formattedMessage = this.formatMessage(level, message, context);

    // In development, use console methods for better formatting
    if (this.isDevelopment) {
      switch (level) {
        case LogLevel.DEBUG:
          console.debug(formattedMessage, metadata || '');
          break;
        case LogLevel.INFO:
          console.info(formattedMessage, metadata || '');
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage, metadata || '');
          break;
        case LogLevel.ERROR:
          console.error(formattedMessage, error || '', metadata || '');
          break;
      }
    } else {
      // In production, use structured JSON logging
      console.log(JSON.stringify(logEntry));
    }
  }

  debug(message: string, context?: LogContext, metadata?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context, undefined, metadata);
  }

  info(message: string, context?: LogContext, metadata?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context, undefined, metadata);
  }

  warn(message: string, context?: LogContext, metadata?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context, undefined, metadata);
  }

  error(message: string, error?: Error, context?: LogContext, metadata?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context, error, metadata);
  }

  // Convenience methods for common operations
  aiOperation(operation: string, message: string, context?: Omit<LogContext, 'component' | 'operation'>): void {
    this.info(message, { 
      component: 'ai-system', 
      operation, 
      ...context 
    });
  }

  jobOperation(operation: string, message: string, context?: Omit<LogContext, 'component' | 'operation'>): void {
    this.info(message, { 
      component: 'job-system', 
      operation, 
      ...context 
    });
  }

  scrapingOperation(operation: string, message: string, context?: Omit<LogContext, 'component' | 'operation'>): void {
    this.info(message, { 
      component: 'scraping-system', 
      operation, 
      ...context 
    });
  }

  adminOperation(operation: string, message: string, context?: Omit<LogContext, 'component' | 'operation'>): void {
    this.info(message, { 
      component: 'admin-panel', 
      operation, 
      ...context 
    });
  }

  // Performance logging
  performance(operation: string, duration: number, context?: LogContext): void {
    this.info(`Performance: ${operation} completed in ${duration}ms`, {
      component: 'performance',
      operation,
      ...context
    }, { duration });
  }

  // API request logging
  apiRequest(method: string, path: string, statusCode: number, duration: number, context?: LogContext): void {
    const level = statusCode >= 400 ? LogLevel.WARN : LogLevel.INFO;
    this.log(level, `API ${method} ${path} - ${statusCode} (${duration}ms)`, {
      component: 'api',
      operation: 'request',
      ...context
    }, undefined, { method, path, statusCode, duration });
  }
}

// Singleton instance
const logger = new Logger();

export { logger };

// Export convenience functions for easier usage
export const log = {
  debug: (message: string, context?: LogContext, metadata?: Record<string, any>) => 
    logger.debug(message, context, metadata),
  
  info: (message: string, context?: LogContext, metadata?: Record<string, any>) => 
    logger.info(message, context, metadata),
  
  warn: (message: string, context?: LogContext, metadata?: Record<string, any>) => 
    logger.warn(message, context, metadata),
  
  error: (message: string, error?: Error, context?: LogContext, metadata?: Record<string, any>) => 
    logger.error(message, error, context, metadata),

  // Specialized logging functions
  ai: (operation: string, message: string, context?: Omit<LogContext, 'component' | 'operation'>) =>
    logger.aiOperation(operation, message, context),

  job: (operation: string, message: string, context?: Omit<LogContext, 'component' | 'operation'>) =>
    logger.jobOperation(operation, message, context),

  scraping: (operation: string, message: string, context?: Omit<LogContext, 'component' | 'operation'>) =>
    logger.scrapingOperation(operation, message, context),

  admin: (operation: string, message: string, context?: Omit<LogContext, 'component' | 'operation'>) =>
    logger.adminOperation(operation, message, context),

  performance: (operation: string, duration: number, context?: LogContext) =>
    logger.performance(operation, duration, context),

  apiRequest: (method: string, path: string, statusCode: number, duration: number, context?: LogContext) =>
    logger.apiRequest(method, path, statusCode, duration, context)
};
