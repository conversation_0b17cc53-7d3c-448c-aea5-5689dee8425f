-- Migration: Add RLS policies for admin operations
-- This ensures that admin operations using the service role key have proper permissions
-- while maintaining security for public operations

-- Add admin policies for tools table
-- Service role can perform all operations on tools
CREATE POLICY "Service role can manage all tools" 
  ON tools 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Add admin policies for categories table
CREATE POLICY "Service role can manage all categories" 
  ON categories 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Add admin policies for reviews table
CREATE POLICY "Service role can manage all reviews" 
  ON reviews 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Add admin policies for tool_submissions table
CREATE POLICY "Service role can manage all tool submissions" 
  ON tool_submissions 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Add admin policies for tags table
CREATE POLICY "Service role can manage all tags" 
  ON tags 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Add admin policies for tool_tags table
CREATE POLICY "Service role can manage all tool tags" 
  ON tool_tags 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Add comment for documentation
COMMENT ON POLICY "Service role can manage all tools" ON tools IS 
  'Allows admin operations using service role key to bypass RLS restrictions for full CRUD access';

-- Verify policies are created correctly
DO $$
BEGIN
  RAISE NOTICE 'Admin RLS policies created successfully';
  RAISE NOTICE 'Service role now has full access to all tables';
END $$;
