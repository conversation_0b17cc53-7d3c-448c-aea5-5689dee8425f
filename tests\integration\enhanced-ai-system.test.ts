/**
 * Enhanced AI System Integration Tests
 * 
 * Comprehensive test suite for validating the enhanced AI system components:
 * - Dual AI provider system (OpenAI + OpenRouter)
 * - Enhanced scraping with scrape.do API
 * - Job processing and monitoring
 * - Bulk processing engine
 * - Editorial workflow
 * - Configuration management
 * - Error handling and recovery
 */

import { createClient } from '@supabase/supabase-js';
import {
  getTestEnvironment,
  createTestSupabaseClient,
  getTestHeaders,
  isTestServiceAvailable
} from '../utils/test-environment';

// Test framework types
interface TestResult {
  name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
  details?: any;
}

interface TestSuite {
  name: string;
  results: TestResult[];
  totalDuration: number;
  passed: number;
  failed: number;
  skipped: number;
}

class EnhancedAISystemTester {
  private testEnv = getTestEnvironment();
  private testSuites: TestSuite[] = [];
  private supabase: ReturnType<typeof createClient>;
  private baseUrl: string;
  private adminApiKey: string;

  constructor() {
    this.baseUrl = this.testEnv.getConfig().baseUrl || 'http://localhost:3000';
    this.adminApiKey = this.testEnv.getConfig().adminApiKey || 'aidude_admin_2024_secure_key_xyz789';
    this.supabase = createClient(
      this.testEnv.getConfig().supabaseUrl || process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      this.testEnv.getConfig().supabaseServiceKey || process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );
  }

  private async getSupabaseClient() {
    if (!isTestServiceAvailable('database')) {
      throw new Error('Database service not available for testing');
    }
    return await createTestSupabaseClient();
  }

  private getBaseUrl(): string {
    return this.testEnv.getConfig().baseUrl || 'http://localhost:3000';
  }

  private getTestHeaders(): Record<string, string> {
    return getTestHeaders();
  }

  async runFullTestSuite(): Promise<void> {
    console.log('🧪 Enhanced AI System - Comprehensive Test Suite');
    console.log('==================================================\n');

    // Run all test suites
    await this.testDatabaseIntegrity();
    await this.testAIProviderSystem();
    await this.testScrapingSystem();
    await this.testJobProcessing();
    await this.testBulkProcessing();
    await this.testEditorialWorkflow();
    await this.testConfigurationManagement();
    await this.testErrorHandling();
    await this.testPerformanceBenchmarks();

    // Generate final report
    this.generateTestReport();
  }

  private async testDatabaseIntegrity(): Promise<void> {
    const suite: TestSuite = {
      name: 'Database Integrity',
      results: [],
      totalDuration: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    console.log('🗄️ Testing Database Integrity...');

    // Test 1: Schema validation
    await this.runTest(suite, 'Schema Validation', async () => {
      const { data: tables, error } = await this.supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');

      if (error) throw new Error(`Schema query failed: ${error.message}`);

      const requiredTables = [
        'tools',
        'ai_generation_jobs',
        'media_assets',
        'editorial_reviews',
        'bulk_processing_jobs',
        'system_configuration'
      ];

      const existingTables = tables?.map((t: any) => t.table_name) || [];
      const missingTables = requiredTables.filter(table => !existingTables.includes(table));

      if (missingTables.length > 0) {
        throw new Error(`Missing tables: ${missingTables.join(', ')}`);
      }

      return { tablesFound: existingTables.length, requiredTables: requiredTables.length };
    });

    // Test 2: Data integrity
    await this.runTest(suite, 'Data Integrity Check', async () => {
      const { data: tools, error } = await this.supabase
        .from('tools')
        .select('id, name, url, ai_generation_status')
        .limit(10);

      if (error) throw new Error(`Data query failed: ${error.message}`);

      const toolsWithStatus = tools?.filter((t: any) => t.ai_generation_status) || [];
      
      return { 
        totalTools: tools?.length || 0,
        toolsWithAIStatus: toolsWithStatus.length
      };
    });

    // Test 3: Foreign key relationships
    await this.runTest(suite, 'Foreign Key Relationships', async () => {
      const { data: jobs, error } = await this.supabase
        .from('ai_generation_jobs')
        .select('id, tool_id, status')
        .limit(5);

      if (error && !error.message.includes('does not exist')) {
        throw new Error(`Foreign key test failed: ${error.message}`);
      }

      return { jobsFound: jobs?.length || 0 };
    });

    this.testSuites.push(suite);
    console.log(`✅ Database Integrity: ${suite.passed}/${suite.results.length} tests passed\n`);
  }

  private async testAIProviderSystem(): Promise<void> {
    const suite: TestSuite = {
      name: 'AI Provider System',
      results: [],
      totalDuration: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    console.log('🤖 Testing AI Provider System...');

    // Test 1: Configuration validation
    await this.runTest(suite, 'Configuration Validation', async () => {
      try {
        const { AIUtils } = await import('../../src/lib/ai');
        const validation = AIUtils.validateConfiguration();
        
        return {
          valid: validation.valid,
          issues: validation.issues || []
        };
      } catch (error: any) {
        throw new Error(`AI configuration test failed: ${error.message}`);
      }
    });

    // Test 2: Health check
    await this.runTest(suite, 'Provider Health Check', async () => {
      try {
        const { performHealthCheck } = await import('../../src/lib/ai');
        const health = await performHealthCheck();
        
        return {
          status: health.status,
          providers: health.providers,
          configurationValid: health.configuration?.valid || false
        };
      } catch (error: any) {
        throw new Error(`Health check failed: ${error.message}`);
      }
    });

    // Test 3: Model selection
    await this.runTest(suite, 'Model Selection Logic', async () => {
      try {
        const { ModelSelector } = await import('../../src/lib/ai/model-selector');
        
        const testCases = [
          { contentSize: 5000, complexity: 'simple' as const, priority: 'speed' as const, features: [] },
          { contentSize: 50000, complexity: 'medium' as const, priority: 'quality' as const, features: ['caching'] },
          { contentSize: 200000, complexity: 'complex' as const, priority: 'quality' as const, features: ['multimodal'] }
        ];

        const results = testCases.map(criteria => {
          const selected = ModelSelector.selectOptimalModel(criteria);
          return {
            criteria,
            selectedProvider: selected.provider,
            selectedModel: selected.model
          };
        });

        return { testCases: results.length, selections: results };
      } catch (error: any) {
        throw new Error(`Model selection test failed: ${error.message}`);
      }
    });

    this.testSuites.push(suite);
    console.log(`✅ AI Provider System: ${suite.passed}/${suite.results.length} tests passed\n`);
  }

  private async testScrapingSystem(): Promise<void> {
    const suite: TestSuite = {
      name: 'Scraping System',
      results: [],
      totalDuration: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    console.log('🕷️ Testing Scraping System...');

    // Test 1: Scrape.do API connectivity
    await this.runTest(suite, 'Scrape.do API Connectivity', async () => {
      try {
        const response = await fetch(`${this.baseUrl}/api/scrape`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': this.adminApiKey
          },
          body: JSON.stringify({
            url: 'https://httpbin.org/html',
            options: {
              captureScreenshot: false,
              extractImages: false
            }
          })
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
          success: data.success,
          hasContent: !!data.data?.content,
          contentLength: data.data?.content?.length || 0
        };
      } catch (error: any) {
        throw new Error(`Scraping API test failed: ${error.message}`);
      }
    });

    // Test 2: Cost optimization
    await this.runTest(suite, 'Cost Optimization Logic', async () => {
      try {
        const { costOptimizer } = await import('../../src/lib/scraping/cost-optimizer');

        const testUrls = [
          'https://github.com/microsoft/vscode',
          'https://openai.com/chatgpt',
          'https://example.com'
        ];

        // Test the categorization functionality instead
        const categorized = costOptimizer.categorizeUrlsByPattern(testUrls);

        return {
          urlsTested: testUrls.length,
          categorized: {
            neverEnhance: categorized.neverEnhance.length,
            alwaysEnhance: categorized.alwaysEnhance.length,
            unknown: categorized.unknown.length
          }
        };
      } catch (error: any) {
        throw new Error(`Cost optimization test failed: ${error.message}`);
      }
    });

    this.testSuites.push(suite);
    console.log(`✅ Scraping System: ${suite.passed}/${suite.results.length} tests passed\n`);
  }

  private async testJobProcessing(): Promise<void> {
    const suite: TestSuite = {
      name: 'Job Processing System',
      results: [],
      totalDuration: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    console.log('⚙️ Testing Job Processing System...');

    // Test 1: Job queue functionality
    await this.runTest(suite, 'Job Queue Operations', async () => {
      try {
        const response = await fetch(`${this.baseUrl}/api/automation/jobs`, {
          method: 'GET',
          headers: {
            'x-api-key': this.adminApiKey
          }
        });

        if (!response.ok) {
          throw new Error(`Jobs API failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
          success: data.success,
          jobCount: data.data?.jobs?.length || 0,
          hasJobsEndpoint: true
        };
      } catch (error: any) {
        throw new Error(`Job queue test failed: ${error.message}`);
      }
    });

    // Test 2: Job creation
    await this.runTest(suite, 'Job Creation', async () => {
      try {
        const response = await fetch(`${this.baseUrl}/api/automation/jobs`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': this.adminApiKey
          },
          body: JSON.stringify({
            type: 'content_generation',
            data: {
              url: 'https://httpbin.org/html',
              priority: 'medium'
            }
          })
        });

        if (!response.ok) {
          throw new Error(`Job creation failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
          success: data.success,
          jobId: data.data?.jobId,
          hasJobCreation: true
        };
      } catch (error: any) {
        throw new Error(`Job creation test failed: ${error.message}`);
      }
    });

    this.testSuites.push(suite);
    console.log(`✅ Job Processing System: ${suite.passed}/${suite.results.length} tests passed\n`);
  }

  private async testBulkProcessing(): Promise<void> {
    const suite: TestSuite = {
      name: 'Bulk Processing Engine',
      results: [],
      totalDuration: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    console.log('📦 Testing Bulk Processing Engine...');

    // Test 1: Bulk processing API
    await this.runTest(suite, 'Bulk Processing API', async () => {
      try {
        const response = await fetch(`${this.baseUrl}/api/admin/bulk-processing`, {
          method: 'GET',
          headers: {
            'x-api-key': this.adminApiKey
          }
        });

        if (!response.ok) {
          throw new Error(`Bulk API failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
          success: data.success || response.ok,
          hasBulkEndpoint: true
        };
      } catch (error: any) {
        throw new Error(`Bulk processing API test failed: ${error.message}`);
      }
    });

    this.testSuites.push(suite);
    console.log(`✅ Bulk Processing Engine: ${suite.passed}/${suite.results.length} tests passed\n`);
  }

  private async testEditorialWorkflow(): Promise<void> {
    const suite: TestSuite = {
      name: 'Editorial Workflow',
      results: [],
      totalDuration: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    console.log('📝 Testing Editorial Workflow...');

    // Test 1: Editorial API endpoints
    await this.runTest(suite, 'Editorial API Endpoints', async () => {
      try {
        const response = await fetch(`${this.baseUrl}/api/admin/editorial/submissions`, {
          method: 'GET',
          headers: {
            'x-api-key': this.adminApiKey
          }
        });

        if (!response.ok) {
          throw new Error(`Editorial API failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
          success: data.success || response.ok,
          hasEditorialEndpoint: true
        };
      } catch (error: any) {
        throw new Error(`Editorial workflow test failed: ${error.message}`);
      }
    });

    this.testSuites.push(suite);
    console.log(`✅ Editorial Workflow: ${suite.passed}/${suite.results.length} tests passed\n`);
  }

  private async testConfigurationManagement(): Promise<void> {
    const suite: TestSuite = {
      name: 'Configuration Management',
      results: [],
      totalDuration: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    console.log('⚙️ Testing Configuration Management...');

    // Test 1: Configuration API
    await this.runTest(suite, 'Configuration API', async () => {
      try {
        const response = await fetch(`${this.baseUrl}/api/admin/config`, {
          method: 'GET',
          headers: {
            'x-api-key': this.adminApiKey
          }
        });

        if (!response.ok) {
          throw new Error(`Config API failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
          success: data.success || response.ok,
          hasConfigEndpoint: true
        };
      } catch (error: any) {
        throw new Error(`Configuration API test failed: ${error.message}`);
      }
    });

    // Test 2: Provider validation
    await this.runTest(suite, 'Provider Validation', async () => {
      try {
        const response = await fetch(`${this.baseUrl}/api/admin/config/test-providers`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': this.adminApiKey
          },
          body: JSON.stringify({
            providers: ['openai', 'openrouter']
          })
        });

        if (!response.ok) {
          throw new Error(`Provider validation failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
          success: data.success || response.ok,
          hasProviderValidation: true
        };
      } catch (error: any) {
        throw new Error(`Provider validation test failed: ${error.message}`);
      }
    });

    this.testSuites.push(suite);
    console.log(`✅ Configuration Management: ${suite.passed}/${suite.results.length} tests passed\n`);
  }

  private async testErrorHandling(): Promise<void> {
    const suite: TestSuite = {
      name: 'Error Handling & Recovery',
      results: [],
      totalDuration: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    console.log('🚨 Testing Error Handling & Recovery...');

    // Test 1: Error manager functionality
    await this.runTest(suite, 'Error Manager', async () => {
      try {
        const { ErrorManager } = await import('../../src/lib/error-handling/error-manager');
        const errorManager = ErrorManager.getInstance();

        // Test error handling
        const testError = new Error('Test error for validation');
        const result = await errorManager.handleError(testError, {
          operation: 'test_operation'
        });

        return {
          hasErrorManager: true,
          handledError: !!result,
          strategy: result.strategy
        };
      } catch (error: any) {
        throw new Error(`Error manager test failed: ${error.message}`);
      }
    });

    // Test 2: Recovery strategies
    await this.runTest(suite, 'Recovery Strategies', async () => {
      try {
        const { RecoveryStrategies } = await import('../../src/lib/error-handling/recovery-strategies');

        // Test retry strategy
        let attempts = 0;
        const testOperation = async () => {
          attempts++;
          if (attempts < 2) {
            throw new Error('Simulated failure');
          }
          return { success: true, attempts };
        };

        const result = await RecoveryStrategies.retry(testOperation, {
          maxRetries: 3,
          delay: 100
        });

        return {
          hasRecoveryStrategies: true,
          retryWorked: result.success,
          attempts: result.attempts
        };
      } catch (error: any) {
        throw new Error(`Recovery strategies test failed: ${error.message}`);
      }
    });

    this.testSuites.push(suite);
    console.log(`✅ Error Handling & Recovery: ${suite.passed}/${suite.results.length} tests passed\n`);
  }

  private async testPerformanceBenchmarks(): Promise<void> {
    const suite: TestSuite = {
      name: 'Performance Benchmarks',
      results: [],
      totalDuration: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    console.log('⚡ Testing Performance Benchmarks...');

    // Test 1: API response times
    await this.runTest(suite, 'API Response Times', async () => {
      const endpoints = [
        '/api/automation/jobs',
        '/api/admin/config',
        '/api/admin/editorial/submissions'
      ];

      const results = [];
      for (const endpoint of endpoints) {
        const startTime = Date.now();
        try {
          const response = await fetch(`${this.baseUrl}${endpoint}`, {
            method: 'GET',
            headers: {
              'x-api-key': this.adminApiKey
            }
          });
          const duration = Date.now() - startTime;
          results.push({
            endpoint,
            duration,
            status: response.status,
            success: response.ok
          });
        } catch (error: any) {
          const duration = Date.now() - startTime;
          results.push({
            endpoint,
            duration,
            status: 'error',
            success: false,
            error: error.message
          });
        }
      }

      const avgResponseTime = results.reduce((sum, r) => sum + r.duration, 0) / results.length;

      return {
        endpoints: results.length,
        averageResponseTime: avgResponseTime,
        results
      };
    });

    // Test 2: Database query performance
    await this.runTest(suite, 'Database Query Performance', async () => {
      const startTime = Date.now();

      const { data: tools, error } = await this.supabase
        .from('tools')
        .select('id, name, url, ai_generation_status')
        .limit(50);

      const duration = Date.now() - startTime;

      if (error) {
        throw new Error(`Database query failed: ${error.message}`);
      }

      return {
        queryDuration: duration,
        recordsReturned: tools?.length || 0,
        performanceGood: duration < 1000 // Less than 1 second
      };
    });

    this.testSuites.push(suite);
    console.log(`✅ Performance Benchmarks: ${suite.passed}/${suite.results.length} tests passed\n`);
  }

  private async runTest(
    suite: TestSuite,
    testName: string,
    testFunction: () => Promise<any>
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      const result = await testFunction();
      const duration = Date.now() - startTime;
      
      suite.results.push({
        name: testName,
        status: 'passed',
        duration,
        details: result
      });
      suite.passed++;
      suite.totalDuration += duration;
      
      console.log(`  ✅ ${testName} (${duration}ms)`);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      suite.results.push({
        name: testName,
        status: 'failed',
        duration,
        error: error.message
      });
      suite.failed++;
      suite.totalDuration += duration;
      
      console.log(`  ❌ ${testName} (${duration}ms): ${error.message}`);
    }
  }

  private generateTestReport(): void {
    console.log('\n📊 ENHANCED AI SYSTEM TEST REPORT');
    console.log('==================================================');
    
    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;
    let totalDuration = 0;

    this.testSuites.forEach(suite => {
      totalTests += suite.results.length;
      totalPassed += suite.passed;
      totalFailed += suite.failed;
      totalDuration += suite.totalDuration;

      console.log(`\n📋 ${suite.name}:`);
      console.log(`   Tests: ${suite.results.length} | Passed: ${suite.passed} | Failed: ${suite.failed}`);
      console.log(`   Duration: ${suite.totalDuration}ms`);
    });

    console.log('\n🎯 OVERALL RESULTS:');
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${totalPassed} (${Math.round((totalPassed / totalTests) * 100)}%)`);
    console.log(`   Failed: ${totalFailed} (${Math.round((totalFailed / totalTests) * 100)}%)`);
    console.log(`   Total Duration: ${totalDuration}ms`);
    
    const status = totalFailed === 0 ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED';
    console.log(`\n🏆 Status: ${status}`);
    console.log('==================================================\n');
  }
}

// Export for use in other test files
export { EnhancedAISystemTester };
export type { TestResult, TestSuite };

// Main execution when run directly
if (require.main === module) {
  const tester = new EnhancedAISystemTester();
  tester.runFullTestSuite().catch(console.error);
}
