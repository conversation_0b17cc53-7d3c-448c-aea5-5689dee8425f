'use client';

import React from 'react';

/**
 * Simple layout test page to verify admin layout positioning
 */
export default function AdminLayoutTestPage() {
  return (
    <div className="space-y-6">
      <div className="bg-green-900 border border-green-700 rounded-lg p-6">
        <h1 className="text-2xl font-bold text-white mb-4">✅ Layout Test - Admin Only Layout</h1>
        <p className="text-green-200 mb-4">
          If you can see ONLY the admin header (not the main site header with AIDUDE logo),
          the sidebar starts BELOW the admin header, and this content is to the RIGHT of the sidebar,
          then the layout is working correctly!
        </p>
        <div className="bg-green-800 p-3 rounded">
          <p className="text-green-100 text-sm">
            <strong>Expected behavior:</strong><br/>
            • NO main site header (AIDUDE logo, search bar, mascot)<br/>
            • ONLY admin header should be visible<br/>
            • Admin header should be fixed at the top<br/>
            • Sidebar should start below the admin header<br/>
            • Main content should be to the right of sidebar<br/>
            • No overlapping between admin header and sidebar
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-blue-900 border border-blue-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-white mb-2">Desktop Layout</h3>
          <ul className="text-blue-200 text-sm space-y-1">
            <li>• Sidebar should be fixed on the left</li>
            <li>• Main content should be to the right</li>
            <li>• Content should have left margin (lg:ml-64)</li>
            <li>• No vertical stacking</li>
          </ul>
        </div>

        <div className="bg-purple-900 border border-purple-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-white mb-2">Mobile Layout</h3>
          <ul className="text-purple-200 text-sm space-y-1">
            <li>• Sidebar should overlay when open</li>
            <li>• Content should take full width</li>
            <li>• Hamburger menu should toggle sidebar</li>
            <li>• Overlay should close sidebar</li>
          </ul>
        </div>
      </div>

      <div className="bg-orange-900 border border-orange-700 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Layout Verification Checklist</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-orange-200 mb-2">Visual Check:</h4>
            <ul className="text-orange-300 text-sm space-y-1">
              <li>□ Sidebar visible on left side</li>
              <li>□ Content area on right side</li>
              <li>□ No content below sidebar</li>
              <li>□ Proper spacing and margins</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-orange-200 mb-2">Responsive Check:</h4>
            <ul className="text-orange-300 text-sm space-y-1">
              <li>□ Mobile menu button works</li>
              <li>□ Sidebar slides in/out smoothly</li>
              <li>□ Overlay closes sidebar</li>
              <li>□ Content adjusts properly</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Updated CSS Classes Applied</h3>
        <div className="text-gray-300 text-sm space-y-2">
          <p><strong>Header:</strong> <code className="bg-zinc-700 px-2 py-1 rounded">fixed top-0 left-0 right-0 z-40 height: var(--admin-header-height)</code></p>
          <p><strong>Sidebar:</strong> <code className="bg-zinc-700 px-2 py-1 rounded">fixed top: var(--admin-header-height) left-0 z-50 height: calc(100vh - var(--admin-header-height))</code></p>
          <p><strong>Main Content:</strong> <code className="bg-zinc-700 px-2 py-1 rounded">lg:ml-64 min-h-screen paddingTop: var(--admin-header-height)</code></p>
          <p><strong>Mobile Overlay:</strong> <code className="bg-zinc-700 px-2 py-1 rounded">fixed top: var(--admin-header-height) left-0 right-0 bottom-0</code></p>
        </div>
        <div className="mt-4 p-3 bg-blue-900 rounded">
          <p className="text-blue-200 text-sm">
            <strong>CSS Variable:</strong> <code className="bg-blue-800 px-2 py-1 rounded">--admin-header-height: 80px</code>
          </p>
        </div>
      </div>
    </div>
  );
}
