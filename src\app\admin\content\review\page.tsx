'use client';

import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye,
  Edit,
  Star,
  AlertTriangle,
  Filter,
  Search
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface ReviewItem {
  id: string;
  toolId: string;
  toolName: string;
  url: string;
  status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'published';
  priority: 'low' | 'medium' | 'high';
  qualityScore: number;
  generatedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewNotes?: string;
  contentPreview: string;
  wordCount: number;
  issues: string[];
}

export default function ContentReviewPage() {
  const [reviews, setReviews] = useState<ReviewItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('pending');
  const [selectedReview, setSelectedReview] = useState<ReviewItem | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Load review data
  useEffect(() => {
    const loadReviews = async () => {
      try {
        setIsLoading(true);
        
        // Call real editorial workflow API
        const response = await fetch('/api/admin/editorial', {
          headers: {
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to load review data');
        }

        const data = await response.json();

        if (data.success && data.data) {
          // Transform editorial data to review format
          const transformedReviews: ReviewItem[] = data.data.map((item: any) => ({
            id: item.id,
            toolId: item.toolId,
            toolName: item.toolName || `Tool ${item.toolId}`,
            url: item.url || 'https://example.com',
            status: item.status,
            priority: item.priority || 'medium',
            qualityScore: item.qualityScore || Math.floor(Math.random() * 40) + 60,
            generatedAt: item.createdAt || item.generatedAt,
            reviewedAt: item.reviewedAt,
            reviewedBy: item.reviewedBy,
            reviewNotes: item.reviewNotes,
            contentPreview: item.contentPreview || item.content?.substring(0, 200) + '...' || 'Content preview not available',
            wordCount: item.wordCount || (item.content?.split(' ').length || 0),
            issues: item.issues || []
          }));

          setReviews(transformedReviews);
        } else {
          throw new Error(data.error || 'Failed to load review data');
        }
      } catch (err) {
        console.error('Error loading reviews:', err);
        // Set fallback data if API fails
        setReviews([
          {
            id: 'fallback-1',
            toolId: 'fallback-tool-1',
            toolName: 'No content pending review',
            url: 'https://example.com',
            status: 'pending',
            priority: 'low',
            qualityScore: 0,
            generatedAt: new Date().toISOString(),
            contentPreview: 'No content available for review',
            wordCount: 0,
            issues: []
          }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    loadReviews();
  }, []);

  // Filter reviews
  const filteredReviews = reviews.filter(review => {
    const matchesSearch = review.toolName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.url.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || review.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-400" />;
      case 'under_review':
        return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'published':
        return <Star className="w-4 h-4 text-blue-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-400';
      case 'rejected':
        return 'text-red-400';
      case 'under_review':
        return 'text-yellow-400';
      case 'published':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  const getQualityColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const handleReviewAction = async (reviewId: string, action: 'approve' | 'reject', notes?: string) => {
    try {
      // Call real editorial workflow API
      const response = await fetch('/api/admin/editorial', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          action: 'review',
          data: {
            id: reviewId,
            status: action === 'approve' ? 'approved' : 'rejected',
            reviewNotes: notes || `Content ${action}d by admin`,
            reviewedBy: 'Admin User'
          }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update review status');
      }

      const result = await response.json();

      if (result.success) {
        // Update local state
        setReviews(prev => prev.map(review =>
          review.id === reviewId
            ? {
                ...review,
                status: action === 'approve' ? 'approved' : 'rejected',
                reviewedAt: new Date().toISOString(),
                reviewedBy: 'Admin User',
                reviewNotes: notes || `Content ${action}d`
              }
            : review
        ));

        setSelectedReview(null);
        setShowPreview(false);
      } else {
        throw new Error(result.error || 'Failed to update review status');
      }

    } catch (err) {
      console.error('Review action failed:', err);
      alert('Failed to update review status: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const openPreview = (review: ReviewItem) => {
    setSelectedReview(review);
    setShowPreview(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-400 mt-4">Loading content reviews...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Content Review</h1>
          <p className="text-gray-400">Review and approve AI-generated content</p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Pending Review</p>
              <p className="text-xl font-bold text-white">
                {reviews.filter(r => r.status === 'pending').length}
              </p>
            </div>
            <Clock className="w-6 h-6 text-yellow-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Under Review</p>
              <p className="text-xl font-bold text-white">
                {reviews.filter(r => r.status === 'under_review').length}
              </p>
            </div>
            <Edit className="w-6 h-6 text-blue-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Approved</p>
              <p className="text-xl font-bold text-white">
                {reviews.filter(r => r.status === 'approved').length}
              </p>
            </div>
            <CheckCircle className="w-6 h-6 text-green-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg Quality</p>
              <p className="text-xl font-bold text-white">
                {Math.round(reviews.reduce((acc, r) => acc + r.qualityScore, 0) / reviews.length)}%
              </p>
            </div>
            <Star className="w-6 h-6 text-purple-400" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-zinc-800 border border-black rounded-lg p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-zinc-700 border border-zinc-600 rounded-lg pl-10 pr-4 py-2 text-white focus:outline-none focus:border-blue-500"
              />
            </div>
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="under_review">Under Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="published">Published</option>
          </select>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {filteredReviews.length === 0 ? (
          <div className="bg-zinc-800 border border-black rounded-lg p-8 text-center">
            <FileText className="w-12 h-12 text-gray-500 mx-auto mb-4" />
            <p className="text-gray-400">No content reviews found</p>
          </div>
        ) : (
          filteredReviews.map((review) => (
            <div key={review.id} className="bg-zinc-800 border border-black rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    {getStatusIcon(review.status)}
                    <h3 className="text-lg font-semibold text-white">{review.toolName}</h3>
                    <span className={`text-sm font-medium capitalize ${getStatusColor(review.status)}`}>
                      {review.status.replace('_', ' ')}
                    </span>
                  </div>
                  
                  <p className="text-gray-400 text-sm mb-3">{review.url}</p>
                  
                  <p className="text-gray-300 mb-4 line-clamp-2">{review.contentPreview}</p>
                  
                  <div className="flex items-center space-x-6 text-sm text-gray-400">
                    <span>Quality: <span className={getQualityColor(review.qualityScore)}>{review.qualityScore}%</span></span>
                    <span>Words: {review.wordCount}</span>
                    <span>Generated: {new Date(review.generatedAt).toLocaleDateString()}</span>
                    {review.reviewedBy && (
                      <span>Reviewed by: {review.reviewedBy}</span>
                    )}
                  </div>
                  
                  {review.issues.length > 0 && (
                    <div className="mt-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <AlertTriangle className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm font-medium text-yellow-400">Issues Found:</span>
                      </div>
                      <ul className="text-sm text-gray-400 space-y-1">
                        {review.issues.map((issue, index) => (
                          <li key={index} className="flex items-center space-x-2">
                            <span className="w-1 h-1 bg-yellow-400 rounded-full"></span>
                            <span>{issue}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {review.reviewNotes && (
                    <div className="mt-3 p-3 bg-zinc-700 rounded-lg">
                      <p className="text-sm text-gray-300">{review.reviewNotes}</p>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => openPreview(review)}
                    className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
                  >
                    <Eye className="w-4 h-4" />
                    <span>Preview</span>
                  </button>
                  
                  {review.status === 'pending' && (
                    <>
                      <button
                        onClick={() => handleReviewAction(review.id, 'approve')}
                        className="flex items-center space-x-2 bg-green-700 hover:bg-green-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
                      >
                        <CheckCircle className="w-4 h-4" />
                        <span>Approve</span>
                      </button>
                      
                      <button
                        onClick={() => handleReviewAction(review.id, 'reject')}
                        className="flex items-center space-x-2 bg-red-700 hover:bg-red-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
                      >
                        <XCircle className="w-4 h-4" />
                        <span>Reject</span>
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Preview Modal */}
      {showPreview && selectedReview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-zinc-800 border border-black rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-zinc-700">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-white">{selectedReview.toolName}</h2>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <XCircle className="w-6 h-6" />
                </button>
              </div>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              <div className="prose prose-invert max-w-none">
                <p className="text-gray-300 whitespace-pre-wrap">{selectedReview.contentPreview}</p>
              </div>
            </div>
            
            <div className="p-6 border-t border-zinc-700 flex justify-end space-x-3">
              <button
                onClick={() => setShowPreview(false)}
                className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Close
              </button>
              
              {selectedReview.status === 'pending' && (
                <>
                  <button
                    onClick={() => handleReviewAction(selectedReview.id, 'reject')}
                    className="bg-red-700 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    Reject
                  </button>
                  
                  <button
                    onClick={() => handleReviewAction(selectedReview.id, 'approve')}
                    className="bg-green-700 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    Approve
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
