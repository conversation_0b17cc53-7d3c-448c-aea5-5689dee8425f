'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Settings,
  Layers,
  FileText,
  Play,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface ContentStats {
  totalTools: number;
  generatedContent: number;
  pendingGeneration: number;
  failedGeneration: number;
  queueSize: number;
  activeJobs: number;
}

interface QueueItem {
  id: string;
  toolName: string;
  url: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  estimatedTime?: string;
}

export default function ContentGenerationPage() {
  const [stats, setStats] = useState<ContentStats | null>(null);
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Mock data for demonstration
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock stats
        setStats({
          totalTools: 87,
          generatedContent: 45,
          pendingGeneration: 12,
          failedGeneration: 3,
          queueSize: 8,
          activeJobs: 2
        });

        // Mock queue data
        setQueue([
          {
            id: '1',
            toolName: 'ChatGPT Alternative',
            url: 'https://example.com/tool1',
            status: 'processing',
            priority: 'high',
            createdAt: '2024-01-15T10:30:00Z',
            estimatedTime: '2 min'
          },
          {
            id: '2',
            toolName: 'AI Image Generator',
            url: 'https://example.com/tool2',
            status: 'pending',
            priority: 'medium',
            createdAt: '2024-01-15T10:25:00Z',
            estimatedTime: '5 min'
          },
          {
            id: '3',
            toolName: 'Code Assistant',
            url: 'https://example.com/tool3',
            status: 'failed',
            priority: 'low',
            createdAt: '2024-01-15T10:20:00Z'
          }
        ]);

      } catch (err) {
        setError('Failed to load content generation data');
        console.error('Error loading data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleStartGeneration = async () => {
    setIsGenerating(true);
    try {
      // Simulate starting content generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      // Refresh data after starting
      window.location.reload();
    } catch {
      setError('Failed to start content generation');
    } finally {
      setIsGenerating(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing':
        return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-400';
      case 'medium':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-400 mt-4">Loading content generation dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900 border border-red-700 rounded-lg p-6">
        <div className="flex items-center space-x-2">
          <AlertCircle className="w-5 h-5 text-red-400" />
          <h3 className="text-lg font-semibold text-red-200">Error</h3>
        </div>
        <p className="text-red-300 mt-2">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-red-800 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Content Generation</h1>
          <p className="text-gray-400">AI-powered content generation and management</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={handleStartGeneration}
            disabled={isGenerating}
            className="flex items-center space-x-2 bg-green-700 hover:bg-green-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            {isGenerating ? (
              <>
                <LoadingSpinner size="sm" />
                <span>Starting...</span>
              </>
            ) : (
              <>
                <Play className="w-4 h-4" />
                <span>Start Generation</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-zinc-800 border border-black rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Generated Content</p>
                <p className="text-2xl font-bold text-white">{stats.generatedContent}</p>
                <p className="text-xs text-gray-500">of {stats.totalTools} tools</p>
              </div>
              <Bot className="w-8 h-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-zinc-800 border border-black rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Queue Size</p>
                <p className="text-2xl font-bold text-white">{stats.queueSize}</p>
                <p className="text-xs text-gray-500">{stats.activeJobs} active jobs</p>
              </div>
              <Layers className="w-8 h-8 text-yellow-400" />
            </div>
          </div>

          <div className="bg-zinc-800 border border-black rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Failed Generation</p>
                <p className="text-2xl font-bold text-white">{stats.failedGeneration}</p>
                <p className="text-xs text-gray-500">requires attention</p>
              </div>
              <AlertCircle className="w-8 h-8 text-red-400" />
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <a
          href="/admin/content/ai-config"
          className="bg-zinc-800 border border-black rounded-lg p-4 hover:bg-zinc-700 transition-colors group"
        >
          <div className="flex items-center space-x-3">
            <Settings className="w-6 h-6 text-blue-400 group-hover:text-blue-300" />
            <div>
              <h3 className="font-medium text-white">AI Configuration</h3>
              <p className="text-sm text-gray-400">Manage AI providers</p>
            </div>
          </div>
        </a>

        <a
          href="/admin/content/queue"
          className="bg-zinc-800 border border-black rounded-lg p-4 hover:bg-zinc-700 transition-colors group"
        >
          <div className="flex items-center space-x-3">
            <Layers className="w-6 h-6 text-yellow-400 group-hover:text-yellow-300" />
            <div>
              <h3 className="font-medium text-white">Generation Queue</h3>
              <p className="text-sm text-gray-400">Monitor progress</p>
            </div>
          </div>
        </a>

        <a
          href="/admin/content/review"
          className="bg-zinc-800 border border-black rounded-lg p-4 hover:bg-zinc-700 transition-colors group"
        >
          <div className="flex items-center space-x-3">
            <FileText className="w-6 h-6 text-green-400 group-hover:text-green-300" />
            <div>
              <h3 className="font-medium text-white">Content Review</h3>
              <p className="text-sm text-gray-400">Editorial workflow</p>
            </div>
          </div>
        </a>

        <a
          href="/admin/content/prompts"
          className="bg-zinc-800 border border-black rounded-lg p-4 hover:bg-zinc-700 transition-colors group"
        >
          <div className="flex items-center space-x-3">
            <Zap className="w-6 h-6 text-purple-400 group-hover:text-purple-300" />
            <div>
              <h3 className="font-medium text-white">Prompt Management</h3>
              <p className="text-sm text-gray-400">Optimize prompts</p>
            </div>
          </div>
        </a>
      </div>

      {/* Generation Queue */}
      <div className="bg-zinc-800 border border-black rounded-lg">
        <div className="p-6 border-b border-zinc-700">
          <h2 className="text-lg font-semibold text-white">Generation Queue</h2>
          <p className="text-gray-400 text-sm">Current content generation jobs</p>
        </div>
        
        <div className="p-6">
          {queue.length === 0 ? (
            <div className="text-center py-8">
              <Layers className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">No items in queue</p>
            </div>
          ) : (
            <div className="space-y-4">
              {queue.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between p-4 bg-zinc-700 rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(item.status)}
                    <div>
                      <h3 className="font-medium text-white">{item.toolName}</h3>
                      <p className="text-sm text-gray-400">{item.url}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <span className={`text-sm font-medium ${getPriorityColor(item.priority)}`}>
                      {item.priority.toUpperCase()}
                    </span>
                    {item.estimatedTime && (
                      <span className="text-sm text-gray-400">{item.estimatedTime}</span>
                    )}
                    <span className="text-sm text-gray-500">
                      {new Date(item.createdAt).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
