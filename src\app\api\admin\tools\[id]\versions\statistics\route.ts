/**
 * Tool Version Statistics API Endpoint
 * Provides version statistics and audit information
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/lib/auth';
import { VersionManager } from '@/lib/versioning/version-manager';
import { VersionAuditLogger } from '@/lib/versioning/audit-logger';

const versionManager = new VersionManager();
const auditLogger = new VersionAuditLogger();

/**
 * GET /api/admin/tools/[id]/versions/statistics
 * Get comprehensive version statistics for a tool
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: toolId } = await params;
    const { searchParams } = new URL(request.url);
    const includeAudit = searchParams.get('includeAudit') === 'true';

    // Get version statistics
    const versionStats = await versionManager.getVersionStatistics(toolId);

    let auditStats = null;
    if (includeAudit) {
      auditStats = await auditLogger.getAuditStatistics(toolId);
    }

    return NextResponse.json({
      success: true,
      data: {
        versionStatistics: versionStats,
        auditStatistics: auditStats,
        metadata: {
          toolId,
          generatedAt: new Date().toISOString(),
          includesAuditData: includeAudit
        }
      }
    });

  } catch (error) {
    console.error('Error fetching version statistics:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch version statistics' 
      },
      { status: 500 }
    );
  }
}
