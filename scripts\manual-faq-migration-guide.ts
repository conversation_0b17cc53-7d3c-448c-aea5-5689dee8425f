#!/usr/bin/env tsx

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function checkAndGuideMigration() {
  console.log('🔍 FAQ Migration Status Check...\n');

  try {
    // Test if faqs column exists
    const { error: testError } = await supabase
      .from('tools')
      .select('id, faqs')
      .limit(1);

    if (testError && (testError.message.includes('column "faqs" does not exist') || testError.message.includes('column tools.faqs does not exist'))) {
      console.log('❌ FAQs column does not exist - Migration required\n');
      console.log('📋 MANUAL MIGRATION STEPS:');
      console.log('==========================================');
      console.log('1. Open your Supabase dashboard');
      console.log('2. Go to SQL Editor');
      console.log('3. Execute the following SQL commands:\n');
      
      console.log('-- Add faqs JSONB column to tools table');
      console.log('ALTER TABLE tools ADD COLUMN faqs JSONB;\n');
      
      console.log('-- Create GIN index for JSONB search and filtering');
      console.log('CREATE INDEX idx_tools_faqs_gin ON tools USING gin(faqs);\n');
      
      console.log('-- Add column comment for documentation');
      console.log(`COMMENT ON COLUMN tools.faqs IS 'JSONB array storing frequently asked questions with structure: [{"question": "...", "answer": "...", "category": "general|pricing|features|support|getting-started", "displayOrder": 0, "priority": 5, "isActive": true, "isFeatured": false, "source": "manual|ai_generated|scraped|user_submitted", "helpScore": 0, "viewCount": 0}]';\n`);
      
      console.log('4. After executing the SQL, run this script again to verify');
      console.log('==========================================\n');
      
      return false;
    } else if (testError) {
      console.error('❌ Unexpected error:', testError.message);
      return false;
    } else {
      console.log('✅ FAQs column exists - Migration already completed!');
      
      // Check if any tools have FAQs
      const { data: toolsWithFaqs, error: faqsError } = await supabase
        .from('tools')
        .select('id, name, faqs')
        .not('faqs', 'is', null)
        .limit(5);

      if (faqsError) {
        console.warn('⚠️  Could not check for existing FAQs:', faqsError.message);
      } else {
        console.log(`\nℹ️  Found ${toolsWithFaqs?.length || 0} tools with FAQs`);
        if (toolsWithFaqs && toolsWithFaqs.length > 0) {
          toolsWithFaqs.forEach(tool => {
            const faqCount = Array.isArray(tool.faqs) ? tool.faqs.length : 0;
            console.log(`   - ${tool.name}: ${faqCount} FAQ(s)`);
          });
        }
      }
      
      console.log('\n🎉 FAQ system is ready for testing!');
      return true;
    }

  } catch (error) {
    console.error('❌ Error checking migration status:', error);
    return false;
  }
}

// Execute the check
if (require.main === module) {
  checkAndGuideMigration().then(success => {
    if (success) {
      console.log('\n📋 Next Steps:');
      console.log('   1. Run: npx tsx scripts/test-faq-system.ts');
      console.log('   2. Test admin FAQ management in the dashboard');
      console.log('   3. Verify ToolQASection component displays FAQs');
    }
    process.exit(success ? 0 : 1);
  }).catch(console.error);
}

export { checkAndGuideMigration };
