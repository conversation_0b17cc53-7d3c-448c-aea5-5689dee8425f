/**
 * Tool Uniqueness Validation
 * 
 * Provides client-side validation for tool uniqueness before form submission
 * Integrates with database constraints to provide user-friendly error messages
 */

import { supabase } from '@/lib/supabase';

export interface UniquenesCheckResult {
  isUnique: boolean;
  conflictingTool?: {
    id: string;
    name: string;
    slug: string;
    website?: string;
  };
  message?: string;
}

/**
 * Check if a tool name is unique (case-insensitive)
 */
export async function checkToolNameUniqueness(
  name: string, 
  excludeId?: string
): Promise<UniquenesCheckResult> {
  try {
    let query = supabase
      .from('tools')
      .select('id, name, slug, website')
      .ilike('name', name);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query.single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    if (data) {
      return {
        isUnique: false,
        conflictingTool: data,
        message: `A tool named "${data.name}" already exists. Please choose a different name.`
      };
    }

    return { isUnique: true };
  } catch (error) {
    console.error('Error checking tool name uniqueness:', error);
    return {
      isUnique: false,
      message: 'Unable to verify name uniqueness. Please try again.'
    };
  }
}

/**
 * Check if a tool slug is unique
 */
export async function checkToolSlugUniqueness(
  slug: string, 
  excludeId?: string
): Promise<UniquenesCheckResult> {
  try {
    let query = supabase
      .from('tools')
      .select('id, name, slug, website')
      .eq('slug', slug);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query.single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    if (data) {
      return {
        isUnique: false,
        conflictingTool: data,
        message: `The URL slug "${slug}" is already taken by "${data.name}". Please choose a different name.`
      };
    }

    return { isUnique: true };
  } catch (error) {
    console.error('Error checking tool slug uniqueness:', error);
    return {
      isUnique: false,
      message: 'Unable to verify URL slug uniqueness. Please try again.'
    };
  }
}

/**
 * Check if a tool website is unique (case-insensitive)
 */
export async function checkToolWebsiteUniqueness(
  website: string, 
  excludeId?: string
): Promise<UniquenesCheckResult> {
  try {
    // Skip check for empty websites
    if (!website || !website.trim()) {
      return { isUnique: true };
    }

    let query = supabase
      .from('tools')
      .select('id, name, slug, website')
      .ilike('website', website);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query.single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    if (data) {
      return {
        isUnique: false,
        conflictingTool: data,
        message: `The website "${website}" is already associated with "${data.name}". Please verify this is a different tool.`
      };
    }

    return { isUnique: true };
  } catch (error) {
    console.error('Error checking tool website uniqueness:', error);
    return {
      isUnique: false,
      message: 'Unable to verify website uniqueness. Please try again.'
    };
  }
}

/**
 * Comprehensive uniqueness check for all tool fields
 */
export async function checkToolUniqueness(
  toolData: {
    name: string;
    slug?: string;
    website?: string;
  },
  excludeId?: string
): Promise<{
  isValid: boolean;
  errors: {
    name?: string;
    slug?: string;
    website?: string;
  };
  conflictingTools: Array<{
    field: 'name' | 'slug' | 'website';
    tool: {
      id: string;
      name: string;
      slug: string;
      website?: string;
    };
  }>;
}> {
  const errors: { name?: string; slug?: string; website?: string } = {};
  const conflictingTools: Array<{
    field: 'name' | 'slug' | 'website';
    tool: { id: string; name: string; slug: string; website?: string };
  }> = [];

  // Check name uniqueness
  const nameCheck = await checkToolNameUniqueness(toolData.name, excludeId);
  if (!nameCheck.isUnique) {
    errors.name = nameCheck.message;
    if (nameCheck.conflictingTool) {
      conflictingTools.push({
        field: 'name',
        tool: nameCheck.conflictingTool
      });
    }
  }

  // Check slug uniqueness if provided
  if (toolData.slug) {
    const slugCheck = await checkToolSlugUniqueness(toolData.slug, excludeId);
    if (!slugCheck.isUnique) {
      errors.slug = slugCheck.message;
      if (slugCheck.conflictingTool) {
        conflictingTools.push({
          field: 'slug',
          tool: slugCheck.conflictingTool
        });
      }
    }
  }

  // Check website uniqueness if provided
  if (toolData.website) {
    const websiteCheck = await checkToolWebsiteUniqueness(toolData.website, excludeId);
    if (!websiteCheck.isUnique) {
      errors.website = websiteCheck.message;
      if (websiteCheck.conflictingTool) {
        conflictingTools.push({
          field: 'website',
          tool: websiteCheck.conflictingTool
        });
      }
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    conflictingTools
  };
}

/**
 * Generate a unique slug from a tool name
 */
export async function generateUniqueSlug(name: string, excludeId?: string): Promise<string> {
  // Generate base slug
  const baseSlug = name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-|-$/g, '');

  let slug = baseSlug;
  let counter = 1;

  // Check uniqueness and increment counter if needed
  while (true) {
    const check = await checkToolSlugUniqueness(slug, excludeId);
    if (check.isUnique) {
      return slug;
    }

    slug = `${baseSlug}-${counter}`;
    counter++;

    // Safety check
    if (counter > 1000) {
      throw new Error('Unable to generate unique slug after 1000 attempts');
    }
  }
}

/**
 * Debounced uniqueness check for real-time validation
 */
export function createDebouncedUniquenessCheck(
  checkFunction: (value: string, excludeId?: string) => Promise<UniquenesCheckResult>,
  delay: number = 500
) {
  let timeoutId: NodeJS.Timeout;

  return (value: string, excludeId?: string): Promise<UniquenesCheckResult> => {
    return new Promise((resolve) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(async () => {
        const result = await checkFunction(value, excludeId);
        resolve(result);
      }, delay);
    });
  };
}

// Pre-configured debounced functions
export const debouncedNameCheck = createDebouncedUniquenessCheck(checkToolNameUniqueness);
export const debouncedSlugCheck = createDebouncedUniquenessCheck(checkToolSlugUniqueness);
export const debouncedWebsiteCheck = createDebouncedUniquenessCheck(checkToolWebsiteUniqueness);
