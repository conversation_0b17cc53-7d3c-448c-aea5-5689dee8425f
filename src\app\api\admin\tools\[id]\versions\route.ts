/**
 * Tool Versions API Endpoints
 * Handles version management operations for tools
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/lib/auth';
import { VersionManager } from '@/lib/versioning/version-manager';
import { VersionListRequest } from '@/lib/types/versioning';

const versionManager = new VersionManager();

/**
 * GET /api/admin/tools/[id]/versions
 * Get version history for a tool
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: toolId } = await params;
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const versionRequest: VersionListRequest = {
      toolId,
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      includeData: searchParams.get('includeData') === 'true',
      changeType: searchParams.get('changeType') as any,
      createdBy: searchParams.get('createdBy') || undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined
    };

    const versionHistory = await versionManager.getVersionHistory(versionRequest);

    return NextResponse.json({
      success: true,
      data: versionHistory
    });

  } catch (error) {
    console.error('Error fetching version history:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch version history' 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/tools/[id]/versions
 * Create a new version of a tool
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: toolId } = await params;
    const body = await request.json();

    // Extract user context from headers
    const userAgent = request.headers.get('user-agent') || undefined;
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const ipAddress = forwardedFor?.split(',')[0] || realIp || undefined;

    const createRequest = {
      toolId,
      changeSummary: body.changeSummary,
      changeType: body.changeType || 'update',
      changeSource: body.changeSource || 'admin_panel',
      createdBy: body.createdBy || 'admin',
      sessionId: body.sessionId,
      requestId: body.requestId || `req_${Date.now()}`,
      ipAddress,
      userAgent
    };

    const result = await versionManager.createVersion(createRequest);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      operationId: result.operationId
    });

  } catch (error) {
    console.error('Error creating version:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to create version' 
      },
      { status: 500 }
    );
  }
}
