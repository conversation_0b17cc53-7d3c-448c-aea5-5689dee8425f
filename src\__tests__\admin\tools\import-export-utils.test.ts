/**
 * @jest-environment jsdom
 */

// Test utility functions for import/export functionality
describe('Import/Export Utility Functions', () => {
  describe('CSV Generation', () => {
    it('should generate proper CSV headers', () => {
      const fields = ['id', 'name', 'slug', 'description'];
      const expectedHeaders = ['Id', 'Name', 'Slug', 'Description'];
      
      const headers = fields.map(field => {
        return field.split('_').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
      });
      
      expect(headers).toEqual(expectedHeaders);
    });

    it('should escape CSV values properly', () => {
      const testCases = [
        { input: 'simple text', expected: 'simple text' },
        { input: 'text with, comma', expected: '"text with, comma"' },
        { input: 'text with "quotes"', expected: '"text with ""quotes"""' },
        { input: 'text with\nnewline', expected: '"text with\nnewline"' },
        { input: null, expected: '' },
        { input: undefined, expected: '' },
      ];

      testCases.forEach(({ input, expected }) => {
        let value = input;
        
        if (value === null || value === undefined) {
          value = '';
        } else if (typeof value === 'string') {
          if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            value = `"${value.replace(/"/g, '""')}"`;
          }
        }
        
        expect(value).toBe(expected);
      });
    });

    it('should handle JSON fields in CSV', () => {
      const jsonField = { type: 'free', features: ['feature1', 'feature2'] };
      const csvValue = JSON.stringify(jsonField);
      const escapedValue = `"${csvValue.replace(/"/g, '""')}"`;
      
      expect(escapedValue).toContain('""type""');
      expect(escapedValue).toContain('""free""');
    });
  });

  describe('JSON Processing', () => {
    it('should detect simple JSON format', () => {
      const data = { urls: ['http://example.com', 'http://test.com'] };
      const format = detectJSONFormat(data);
      expect(format).toBe('simple');
    });

    it('should detect detailed JSON format', () => {
      const data = { 
        tools: [
          { name: 'Tool 1', url: 'http://example.com' },
          { name: 'Tool 2', url: 'http://test.com' }
        ] 
      };
      const format = detectJSONFormat(data);
      expect(format).toBe('detailed');
    });

    it('should detect mapping JSON format', () => {
      const data = { 
        fieldMapping: { name: 'tool_name', url: 'website' },
        data: [
          { tool_name: 'Tool 1', website: 'http://example.com' }
        ]
      };
      const format = detectJSONFormat(data);
      expect(format).toBe('mapping');
    });

    it('should throw error for unknown JSON format', () => {
      const data = { unknown: 'format' };
      expect(() => detectJSONFormat(data)).toThrow('Unknown JSON format');
    });
  });

  describe('URL Validation', () => {
    it('should validate URLs correctly', () => {
      const validUrls = [
        'https://example.com',
        'http://test.com',
        'https://subdomain.example.com/path',
        'http://localhost:3000',
      ];

      const invalidUrls = [
        'not-a-url',
        'example.com',
        '',
      ];

      validUrls.forEach(url => {
        expect(() => new URL(url)).not.toThrow();
      });

      invalidUrls.forEach(url => {
        if (url) {
          expect(() => new URL(url)).toThrow();
        }
      });
    });
  });

  describe('Slug Generation', () => {
    it('should generate valid slugs', () => {
      const testCases = [
        { input: 'Test Tool Name', expected: 'test-tool-name' },
        { input: 'Tool with Special @#$ Characters!', expected: 'tool-with-special-characters' },
        { input: 'Multiple   Spaces', expected: 'multiple-spaces' },
        { input: 'Tool-with-dashes', expected: 'tool-with-dashes' },
        { input: '123 Numeric Tool', expected: '123-numeric-tool' },
      ];

      testCases.forEach(({ input, expected }) => {
        const slug = generateSlug(input);
        expect(slug).toBe(expected);
      });
    });

    it('should handle edge cases in slug generation', () => {
      expect(generateSlug('')).toBe('');
      expect(generateSlug('   ')).toBe('');
      expect(generateSlug('---')).toBe('');
      expect(generateSlug('Special!@#$%^&*()Characters')).toBe('specialcharacters');
    });
  });

  describe('Data Validation', () => {
    it('should validate required fields', () => {
      const validTool = {
        name: 'Test Tool',
        link: '/tools/test-tool',
        description: 'A test tool'
      };

      const invalidTools = [
        { link: '/tools/test' }, // Missing name
        { name: 'Test' }, // Missing link
        {}, // Missing both
      ];

      expect(validateToolData(validTool)).toBe(true);
      invalidTools.forEach(tool => {
        expect(validateToolData(tool)).toBe(false);
      });
    });

    it('should validate file size limits', () => {
      const maxSize = 50 * 1024 * 1024; // 50MB
      
      expect(validateFileSize(1024, maxSize)).toBe(true);
      expect(validateFileSize(maxSize, maxSize)).toBe(true);
      expect(validateFileSize(maxSize + 1, maxSize)).toBe(false);
    });

    it('should validate file extensions', () => {
      const allowedExtensions = ['.csv', '.json'];
      
      expect(validateFileExtension('test.csv', allowedExtensions)).toBe(true);
      expect(validateFileExtension('test.json', allowedExtensions)).toBe(true);
      expect(validateFileExtension('test.txt', allowedExtensions)).toBe(false);
      expect(validateFileExtension('test.xlsx', allowedExtensions)).toBe(false);
    });
  });

  describe('Duplicate Detection', () => {
    it('should detect duplicate names', () => {
      const existingTools = [
        { name: 'Existing Tool 1', slug: 'existing-tool-1' },
        { name: 'Existing Tool 2', slug: 'existing-tool-2' },
      ];

      const newTool = { name: 'Existing Tool 1', slug: 'existing-tool-1' };
      
      expect(isDuplicateName(newTool.name, existingTools)).toBe(true);
      expect(isDuplicateSlug(newTool.slug, existingTools)).toBe(true);
      
      const uniqueTool = { name: 'New Tool', slug: 'new-tool' };
      expect(isDuplicateName(uniqueTool.name, existingTools)).toBe(false);
      expect(isDuplicateSlug(uniqueTool.slug, existingTools)).toBe(false);
    });
  });
});

// Utility functions to test (these would normally be imported from the actual implementation)
function detectJSONFormat(data: any): string {
  if (Array.isArray(data.urls)) return 'simple';
  if (Array.isArray(data.tools)) return 'detailed';
  if (data.fieldMapping && Array.isArray(data.data)) return 'mapping';
  throw new Error('Unknown JSON format');
}

function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '') // Remove leading/trailing dashes
    .trim();
}

function validateToolData(tool: any): boolean {
  return !!(tool.name && tool.link);
}

function validateFileSize(size: number, maxSize: number): boolean {
  return size <= maxSize;
}

function validateFileExtension(filename: string, allowedExtensions: string[]): boolean {
  const extension = '.' + filename.split('.').pop()?.toLowerCase();
  return allowedExtensions.includes(extension);
}

function isDuplicateName(name: string, existingTools: any[]): boolean {
  return existingTools.some(tool => tool.name.toLowerCase() === name.toLowerCase());
}

function isDuplicateSlug(slug: string, existingTools: any[]): boolean {
  return existingTools.some(tool => tool.slug === slug);
}
