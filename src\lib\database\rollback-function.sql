-- Rollback Function for Tool Versioning System
-- Safely rollbacks a tool to a specific version with transaction integrity

CREATE OR REPLACE FUNCTION rollback_tool_to_version(
    p_tool_id VARCHAR(255),
    p_target_version_data JSONB,
    p_rollback_reason TEXT,
    p_performed_by <PERSON><PERSON><PERSON><PERSON>(255)
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_current_tool RECORD;
    v_rollback_result JSONB;
    v_updated_tool RECORD;
BEGIN
    -- Start transaction (implicit in function)
    
    -- Get current tool data for validation
    SELECT * INTO v_current_tool
    FROM tools
    WHERE id = p_tool_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Tool not found: %', p_tool_id;
    END IF;
    
    -- Validate target version data
    IF p_target_version_data IS NULL THEN
        RAISE EXCEPTION 'Target version data cannot be null';
    END IF;
    
    -- Extract fields from target version data and update tool
    UPDATE tools SET
        name = COALESCE((p_target_version_data->>'name')::VARCHAR(255), name),
        slug = COALESCE((p_target_version_data->>'slug')::VARCHAR(255), slug),
        logo_url = (p_target_version_data->>'logoUrl')::TEXT,
        description = (p_target_version_data->>'description')::TEXT,
        short_description = (p_target_version_data->>'shortDescription')::VARCHAR(150),
        detailed_description = (p_target_version_data->>'detailedDescription')::TEXT,
        link = COALESCE((p_target_version_data->>'link')::TEXT, link),
        website = (p_target_version_data->>'website')::TEXT,
        category_id = (p_target_version_data->>'categoryId')::VARCHAR(255),
        subcategory = (p_target_version_data->>'subcategory')::VARCHAR(255),
        company = (p_target_version_data->>'company')::VARCHAR(255),
        is_verified = COALESCE((p_target_version_data->>'isVerified')::BOOLEAN, is_verified),
        is_claimed = COALESCE((p_target_version_data->>'isClaimed')::BOOLEAN, is_claimed),
        features = (p_target_version_data->'features'),
        screenshots = (p_target_version_data->'screenshots'),
        pricing = (p_target_version_data->'pricing'),
        social_links = (p_target_version_data->'socialLinks'),
        pros_and_cons = (p_target_version_data->'prosAndCons'),
        haiku = (p_target_version_data->'haiku'),
        hashtags = (p_target_version_data->'hashtags'),
        releases = (p_target_version_data->'releases'),
        claim_info = (p_target_version_data->'claimInfo'),
        meta_title = (p_target_version_data->>'metaTitle')::VARCHAR(255),
        meta_description = (p_target_version_data->>'metaDescription')::TEXT,
        content_status = COALESCE((p_target_version_data->>'contentStatus')::VARCHAR(20), content_status),
        generated_content = (p_target_version_data->'generatedContent'),
        
        -- Enhanced AI system fields
        scraped_data = (p_target_version_data->'scrapedData'),
        ai_generation_status = COALESCE((p_target_version_data->>'aiGenerationStatus')::VARCHAR(20), ai_generation_status),
        last_scraped_at = CASE 
            WHEN p_target_version_data->>'lastScrapedAt' IS NOT NULL 
            THEN (p_target_version_data->>'lastScrapedAt')::TIMESTAMP WITH TIME ZONE 
            ELSE last_scraped_at 
        END,
        editorial_review_id = CASE 
            WHEN p_target_version_data->>'editorialReviewId' IS NOT NULL 
            THEN (p_target_version_data->>'editorialReviewId')::UUID 
            ELSE editorial_review_id 
        END,
        ai_generation_job_id = CASE 
            WHEN p_target_version_data->>'aiGenerationJobId' IS NOT NULL 
            THEN (p_target_version_data->>'aiGenerationJobId')::UUID 
            ELSE ai_generation_job_id 
        END,
        submission_type = COALESCE((p_target_version_data->>'submissionType')::VARCHAR(20), submission_type),
        submission_source = (p_target_version_data->>'submissionSource')::VARCHAR(50),
        content_quality_score = CASE 
            WHEN p_target_version_data->>'contentQualityScore' IS NOT NULL 
            THEN (p_target_version_data->>'contentQualityScore')::INTEGER 
            ELSE content_quality_score 
        END,
        last_ai_update = CASE 
            WHEN p_target_version_data->>'lastAiUpdate' IS NOT NULL 
            THEN (p_target_version_data->>'lastAiUpdate')::TIMESTAMP WITH TIME ZONE 
            ELSE last_ai_update 
        END,
        
        -- FAQ field
        faqs = (p_target_version_data->'faqs'),
        
        -- Update timestamp
        updated_at = CURRENT_TIMESTAMP
        
    WHERE id = p_tool_id
    RETURNING *;
    
    -- Get the updated tool data
    GET DIAGNOSTICS v_updated_tool = ROW_COUNT;
    
    IF v_updated_tool = 0 THEN
        RAISE EXCEPTION 'Failed to update tool during rollback';
    END IF;
    
    -- Prepare result
    v_rollback_result := jsonb_build_object(
        'success', true,
        'tool_id', p_tool_id,
        'rollback_reason', p_rollback_reason,
        'performed_by', p_performed_by,
        'rollback_timestamp', CURRENT_TIMESTAMP,
        'message', 'Tool successfully rolled back'
    );
    
    RETURN v_rollback_result;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log error details
        RAISE EXCEPTION 'Rollback failed for tool %: % (SQLSTATE: %)', 
            p_tool_id, SQLERRM, SQLSTATE;
END;
$$;

-- Grant execute permission to the service role
-- This should be run with appropriate permissions
-- GRANT EXECUTE ON FUNCTION rollback_tool_to_version TO service_role;

-- Create helper function to validate version data structure
CREATE OR REPLACE FUNCTION validate_version_data(version_data JSONB)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    -- Check for required fields
    IF version_data IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Check for essential fields that must exist
    IF NOT (version_data ? 'name' AND version_data ? 'link') THEN
        RETURN FALSE;
    END IF;
    
    -- Validate data types for critical fields
    BEGIN
        -- Test boolean fields
        IF version_data ? 'isVerified' AND 
           NOT (version_data->>'isVerified')::BOOLEAN IS NOT NULL THEN
            RETURN FALSE;
        END IF;
        
        IF version_data ? 'isClaimed' AND 
           NOT (version_data->>'isClaimed')::BOOLEAN IS NOT NULL THEN
            RETURN FALSE;
        END IF;
        
        -- Test timestamp fields
        IF version_data ? 'lastScrapedAt' AND version_data->>'lastScrapedAt' != '' AND
           NOT (version_data->>'lastScrapedAt')::TIMESTAMP WITH TIME ZONE IS NOT NULL THEN
            RETURN FALSE;
        END IF;
        
        -- Test integer fields
        IF version_data ? 'contentQualityScore' AND 
           NOT (version_data->>'contentQualityScore')::INTEGER IS NOT NULL THEN
            RETURN FALSE;
        END IF;
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN FALSE;
    END;
    
    RETURN TRUE;
END;
$$;

-- Create function to get tool version count
CREATE OR REPLACE FUNCTION update_tool_version_count(p_tool_id VARCHAR(255))
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_count INTEGER;
BEGIN
    -- Count versions for the tool
    SELECT COUNT(*) INTO v_count
    FROM tool_versions
    WHERE tool_id = p_tool_id;
    
    -- Update the tools table
    UPDATE tools 
    SET version_count = v_count
    WHERE id = p_tool_id;
    
    RETURN v_count;
END;
$$;

-- Create trigger to automatically update version count
CREATE OR REPLACE FUNCTION trigger_update_version_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update version count for the affected tool
    PERFORM update_tool_version_count(
        CASE 
            WHEN TG_OP = 'DELETE' THEN OLD.tool_id
            ELSE NEW.tool_id
        END
    );
    
    RETURN CASE 
        WHEN TG_OP = 'DELETE' THEN OLD
        ELSE NEW
    END;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on tool_versions table
DROP TRIGGER IF EXISTS trigger_update_tool_version_count ON tool_versions;
CREATE TRIGGER trigger_update_tool_version_count
    AFTER INSERT OR UPDATE OR DELETE ON tool_versions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_version_count();

-- Create function to initialize versions for existing tools
CREATE OR REPLACE FUNCTION initialize_tool_versions()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    tool_record RECORD;
    version_count INTEGER := 0;
BEGIN
    -- Loop through all tools that don't have versions yet
    FOR tool_record IN 
        SELECT t.* 
        FROM tools t
        LEFT JOIN tool_versions tv ON t.id = tv.tool_id
        WHERE tv.tool_id IS NULL
    LOOP
        -- Create initial version for each tool
        INSERT INTO tool_versions (
            tool_id,
            version_data,
            change_summary,
            created_by,
            is_current,
            change_type,
            change_source
        ) VALUES (
            tool_record.id,
            row_to_json(tool_record)::jsonb,
            'Initial version created during migration',
            'system',
            true,
            'create',
            'admin_panel'
        );
        
        version_count := version_count + 1;
    END LOOP;
    
    RETURN version_count;
END;
$$;
