'use client';

import React from 'react';
import { 
  Wrench, 
  CheckCircle, 
  FileText, 
  Archive,
  TrendingUp,
  Users,
  Activity,
  Clock
} from 'lucide-react';

interface AdminDashboardStatsProps {
  stats: {
    totalTools: number;
    publishedTools: number;
    draftTools: number;
    archivedTools: number;
    totalUsers?: number;
    activeJobs?: number;
    pendingReviews?: number;
    systemUptime?: string;
  };
}

interface StatCard {
  id: string;
  title: string;
  value: string | number;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  color: string;
  trend?: {
    value: number;
    direction: 'up' | 'down';
    period: string;
  };
}

export function AdminDashboardStats({ stats }: AdminDashboardStatsProps) {
  const statCards: StatCard[] = [
    {
      id: 'total-tools',
      title: 'Total Tools',
      value: stats.totalTools,
      icon: Wrench,
      color: 'rgb(255, 150, 0)',
      trend: {
        value: 12,
        direction: 'up',
        period: 'this week'
      }
    },
    {
      id: 'published-tools',
      title: 'Published',
      value: stats.publishedTools,
      icon: CheckCircle,
      color: 'rgb(34, 197, 94)',
      trend: {
        value: 8,
        direction: 'up',
        period: 'this week'
      }
    },
    {
      id: 'draft-tools',
      title: 'Draft',
      value: stats.draftTools,
      icon: FileText,
      color: 'rgb(234, 179, 8)',
      trend: {
        value: 3,
        direction: 'down',
        period: 'this week'
      }
    },
    {
      id: 'archived-tools',
      title: 'Archived',
      value: stats.archivedTools,
      icon: Archive,
      color: 'rgb(239, 68, 68)'
    }
  ];

  // Add additional stats if provided
  if (stats.totalUsers !== undefined) {
    statCards.push({
      id: 'total-users',
      title: 'Total Users',
      value: stats.totalUsers,
      icon: Users,
      color: 'rgb(59, 130, 246)'
    });
  }

  if (stats.activeJobs !== undefined) {
    statCards.push({
      id: 'active-jobs',
      title: 'Active Jobs',
      value: stats.activeJobs,
      icon: Activity,
      color: 'rgb(168, 85, 247)'
    });
  }

  if (stats.pendingReviews !== undefined) {
    statCards.push({
      id: 'pending-reviews',
      title: 'Pending Reviews',
      value: stats.pendingReviews,
      icon: Clock,
      color: 'rgb(245, 101, 101)'
    });
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((card) => {
        const Icon = card.icon;
        
        return (
          <div
            key={card.id}
            className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            {/* Header with icon and title */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: `${card.color}20` }}
                >
                  <Icon 
                    size={20} 
                    style={{ color: card.color }}
                  />
                </div>
                <h3 className="text-sm font-medium text-gray-300">
                  {card.title}
                </h3>
              </div>
            </div>

            {/* Main value */}
            <div className="mb-3">
              <p 
                className="text-3xl font-bold"
                style={{ color: card.color }}
              >
                {typeof card.value === 'number' ? card.value.toLocaleString() : card.value}
              </p>
            </div>

            {/* Trend indicator */}
            {card.trend && (
              <div className="flex items-center space-x-1">
                <TrendingUp 
                  size={14} 
                  className={`${
                    card.trend.direction === 'up' 
                      ? 'text-green-400 rotate-0' 
                      : 'text-red-400 rotate-180'
                  }`}
                />
                <span 
                  className={`text-xs font-medium ${
                    card.trend.direction === 'up' 
                      ? 'text-green-400' 
                      : 'text-red-400'
                  }`}
                >
                  {card.trend.direction === 'up' ? '+' : '-'}{card.trend.value}
                </span>
                <span className="text-xs text-gray-400">
                  {card.trend.period}
                </span>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
