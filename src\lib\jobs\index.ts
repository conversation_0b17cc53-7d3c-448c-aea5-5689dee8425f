// Main exports for the Enhanced Job System
export { getEnhancedJobQueue } from './enhanced-queue';
export { getJobManager } from './job-manager';
export { getProgressTracker } from './progress-tracker';
export { getWebSocketManager } from './websocket-manager';
export { initializeJobQueue } from './init';
export * from './types';
export * from './handlers';

// Backward compatibility export
export { getEnhancedJobQueue as getJobQueue } from './enhanced-queue';

// Convenience functions
import { getEnhancedJobQueue } from './enhanced-queue';
import { getJobManager } from './job-manager';
import { JobType, JobPriority } from './types';

/**
 * Queue a tool submission for processing
 */
export async function queueToolSubmission(data: {
  url: string;
  name: string;
  description?: string;
  category?: string;
  submitterEmail: string;
  submitterName?: string;
}) {
  const jobManager = getJobManager();
  return jobManager.createJob(JobType.TOOL_SUBMISSION, data, {
    priority: JobPriority.NORMAL,
  });
}

/**
 * Queue content generation for a tool
 */
export async function queueContentGeneration(data: {
  url: string;
  scrapedData: any;
  toolId?: string;
}) {
  const jobManager = getJobManager();
  return jobManager.createJob(JobType.CONTENT_GENERATION, data, {
    priority: JobPriority.NORMAL,
  });
}

/**
 * Queue web scraping for a URL
 */
export async function queueWebScraping(data: {
  url: string;
  options?: any;
}) {
  const jobManager = getJobManager();
  return jobManager.createJob(JobType.WEB_SCRAPING, data, {
    priority: JobPriority.NORMAL,
  });
}

// Enhanced Job Management Functions

/**
 * Create a job with enhanced features (progress tracking, WebSocket updates)
 */
export async function createEnhancedJob(
  type: JobType,
  data: any,
  options: any = {}
) {
  const jobManager = getJobManager();
  return jobManager.createJob(type, data, options);
}

/**
 * Get job with real-time progress data
 */
export async function getEnhancedJob(id: string) {
  const jobManager = getJobManager();
  return jobManager.getJob(id);
}

/**
 * Pause a job with enhanced controls
 */
export async function pauseJob(id: string, reason?: string) {
  const jobManager = getJobManager();
  return jobManager.pauseJob(id, { reason });
}

/**
 * Resume a paused job
 */
export async function resumeJob(id: string, reason?: string) {
  const jobManager = getJobManager();
  return jobManager.resumeJob(id, { reason });
}

/**
 * Stop a job with enhanced controls
 */
export async function stopJob(id: string, reason?: string) {
  const jobManager = getJobManager();
  return jobManager.stopJob(id, { reason });
}

/**
 * Get comprehensive queue statistics
 */
export async function getQueueStatistics() {
  const jobManager = getJobManager();
  return jobManager.getQueueStats();
}

/**
 * Subscribe to job progress updates
 */
export function subscribeToJobProgress(
  jobId: string,
  onProgress: (job: any, progress: number, details?: any) => void
) {
  const jobManager = getJobManager();
  return jobManager.subscribeToJobEvents(jobId, {
    onProgressUpdate: onProgress,
  });
}

/**
 * Queue an email notification
 */
export async function queueEmailNotification(data: {
  to: string | string[];
  subject: string;
  template: string;
  data: any;
}) {
  const jobManager = getJobManager();
  return jobManager.createJob(JobType.EMAIL_NOTIFICATION, data, {
    priority: JobPriority.LOW,
  });
}
