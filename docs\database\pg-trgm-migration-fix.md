# PostgreSQL pg_trgm Extension Migration Fix

## Problem

The migration file `src/lib/database/migrations/003_add_uniqueness_constraints.sql` was failing with the error:

```
Error Code: 42883 
Message: "function similarity(text, text) does not exist"
Location: Line 162 in the migration file
```

This error occurred because the `similarity()` function is part of PostgreSQL's `pg_trgm` extension, which needs to be explicitly enabled before it can be used.

## Solution

The migration file has been fixed by adding the `pg_trgm` extension enablement at the beginning of the file:

```sql
-- =====================================================
-- ENABLE REQUIRED EXTENSIONS
-- =====================================================

-- Enable pg_trgm extension for similarity() function used in duplicate detection
-- This extension provides trigram matching for fuzzy text similarity
CREATE EXTENSION IF NOT EXISTS pg_trgm;
```

## What the pg_trgm Extension Provides

The `pg_trgm` extension provides:
- **Trigram matching**: Breaks text into 3-character sequences for similarity comparison
- **similarity() function**: Returns a similarity score between 0 and 1
- **Fuzzy text matching**: Useful for finding potential duplicates with slight variations
- **Performance indexes**: GIN and GiST indexes for fast similarity searches

## Usage in the Migration

The extension is used in the `potential_duplicate_tools` view to identify tools that might be duplicates:

```sql
CREATE OR REPLACE VIEW potential_duplicate_tools AS
SELECT 
  t1.id as tool1_id,
  t1.name as tool1_name,
  t1.slug as tool1_slug,
  t2.id as tool2_id,
  t2.name as tool2_name,
  t2.slug as tool2_slug,
  similarity(LOWER(t1.name), LOWER(t2.name)) as name_similarity
FROM tools t1
JOIN tools t2 ON t1.id < t2.id
WHERE similarity(LOWER(t1.name), LOWER(t2.name)) > 0.8
ORDER BY name_similarity DESC;
```

## How to Apply the Migration

### Option 1: Using the Migration Script

1. **Test the extension first** (optional but recommended):
   ```bash
   npm run db:test-pg-trgm
   ```

2. **Apply the migration**:
   ```bash
   npm run db:migrate:003
   ```

### Option 2: Manual Application

1. **Copy the SQL** from `src/lib/database/migrations/003_add_uniqueness_constraints.sql`

2. **Open Supabase Dashboard** → SQL Editor

3. **Paste and execute** the SQL

4. **Mark as completed**:
   ```bash
   npm run db:migrate:003 -- --auto-complete
   ```

## Verification

After applying the migration, you can verify it worked by:

1. **Check the extension is enabled**:
   ```sql
   SELECT * FROM pg_extension WHERE extname = 'pg_trgm';
   ```

2. **Test the similarity function**:
   ```sql
   SELECT similarity('ChatGPT', 'Chat GPT');
   ```

3. **Query the duplicate detection view**:
   ```sql
   SELECT * FROM potential_duplicate_tools LIMIT 5;
   ```

## Compatibility

- **PostgreSQL Version**: Requires PostgreSQL 9.1 or later
- **Supabase**: Fully supported (pg_trgm is a standard extension)
- **Permissions**: Extension creation requires superuser privileges (handled by Supabase)

## Benefits

After applying this migration, you'll have:

✅ **Unique constraints** preventing duplicate tool names, slugs, and websites  
✅ **Performance indexes** for faster searches  
✅ **Validation functions** for checking duplicates  
✅ **Automatic slug generation** from tool names  
✅ **Duplicate detection view** using fuzzy text matching  
✅ **Trigram similarity matching** for finding potential duplicates  

## Troubleshooting

If you encounter issues:

1. **Extension not available**: Ensure you're using a PostgreSQL version that supports pg_trgm
2. **Permission denied**: Ensure you're using the service role key with sufficient privileges
3. **Function still not found**: Try restarting your database connection after enabling the extension

## Related Files

- **Migration file**: `src/lib/database/migrations/003_add_uniqueness_constraints.sql`
- **Test script**: `scripts/test-pg-trgm-migration.ts`
- **Apply script**: `scripts/apply-uniqueness-constraints-migration.ts`
- **Package.json scripts**: `db:test-pg-trgm`, `db:migrate:003`
