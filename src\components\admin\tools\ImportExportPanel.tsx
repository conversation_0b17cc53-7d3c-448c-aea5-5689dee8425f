'use client';

import { useState, useRef } from 'react';
import { Download, Upload, FileText, Database, AlertCircle, CheckCircle, X } from 'lucide-react';

interface ImportExportPanelProps {
  onImportComplete?: () => void;
  onError?: (error: string) => void;
}

interface ImportError {
  row: number;
  error: string;
  data?: Record<string, unknown>;
}

interface ImportDuplicate {
  row: number;
  name: string;
  slug: string;
}

interface ImportResult {
  success: boolean;
  imported: number;
  skipped: number;
  errors: ImportError[];
  duplicates: ImportDuplicate[];
}

interface PreviewData {
  totalRows: number;
  sampleData: Record<string, unknown>[];
  fields: string[];
}

export function ImportExportPanel({ onImportComplete, onError }: ImportExportPanelProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [duplicateStrategy, setDuplicateStrategy] = useState<'skip' | 'update' | 'error'>('skip');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleExport = async (format: 'json' | 'csv') => {
    setIsExporting(true);
    try {
      const response = await fetch(`/api/admin/tools/export?format=${format}`, {
        headers: {
          'x-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || '',
        },
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = response.headers.get('content-disposition')?.split('filename=')[1]?.replace(/"/g, '') || `tools-export.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

    } catch (error) {
      onError?.(error instanceof Error ? error.message : 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    try {
      // First, get preview of the data
      const formData = new FormData();
      formData.append('file', file);
      formData.append('preview', 'true');

      const response = await fetch('/api/admin/tools/import', {
        method: 'POST',
        headers: {
          'x-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || '',
        },
        body: formData,
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error);
      }

      setPreviewData(result);
      setShowImportDialog(true);

    } catch (error) {
      onError?.(error instanceof Error ? error.message : 'Failed to process file');
    } finally {
      setIsImporting(false);
    }
  };

  const handleImportConfirm = async () => {
    if (!fileInputRef.current?.files?.[0]) return;

    setIsImporting(true);
    try {
      const formData = new FormData();
      formData.append('file', fileInputRef.current.files[0]);
      formData.append('duplicateStrategy', duplicateStrategy);

      const response = await fetch('/api/admin/tools/import', {
        method: 'POST',
        headers: {
          'x-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || '',
        },
        body: formData,
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error);
      }

      setImportResult(result.result);
      setShowImportDialog(false);
      onImportComplete?.();

    } catch (error) {
      onError?.(error instanceof Error ? error.message : 'Import failed');
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <div className="bg-zinc-800 border border-black rounded-lg p-6">
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <Database className="w-5 h-5 mr-2" />
        Import/Export Tools
      </h3>

      {/* Export Section */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-300 mb-3">Export Tools</h4>
        <div className="flex space-x-3">
          <button
            onClick={() => handleExport('json')}
            disabled={isExporting}
            className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white rounded-lg transition-colors"
          >
            <Download className="w-4 h-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Export JSON'}
          </button>
          <button
            onClick={() => handleExport('csv')}
            disabled={isExporting}
            className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white rounded-lg transition-colors"
          >
            <FileText className="w-4 h-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Export CSV'}
          </button>
        </div>
      </div>

      {/* Import Section */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-300 mb-3">Import Tools</h4>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={isImporting}
            className="flex items-center px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-orange-800 text-white rounded-lg transition-colors"
          >
            <Upload className="w-4 h-4 mr-2" />
            {isImporting ? 'Processing...' : 'Import File'}
          </button>
          <span className="text-sm text-gray-400">
            Supports CSV and JSON files (max 50MB)
          </span>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv,.json"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* Import Result */}
      {importResult && (
        <div className="mb-6 p-4 bg-zinc-700 rounded-lg">
          <h4 className="text-sm font-medium text-white mb-2 flex items-center">
            <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
            Import Complete
          </h4>
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Imported:</span>
              <span className="text-green-400 ml-2 font-medium">{importResult.imported}</span>
            </div>
            <div>
              <span className="text-gray-400">Skipped:</span>
              <span className="text-yellow-400 ml-2 font-medium">{importResult.skipped}</span>
            </div>
            <div>
              <span className="text-gray-400">Errors:</span>
              <span className="text-red-400 ml-2 font-medium">{importResult.errors.length}</span>
            </div>
          </div>
          
          {importResult.errors.length > 0 && (
            <div className="mt-3">
              <details className="text-sm">
                <summary className="text-red-400 cursor-pointer">View Errors</summary>
                <div className="mt-2 space-y-1 max-h-32 overflow-y-auto">
                  {importResult.errors.map((error, index) => (
                    <div key={index} className="text-red-300 text-xs">
                      Row {error.row}: {error.error}
                    </div>
                  ))}
                </div>
              </details>
            </div>
          )}
        </div>
      )}

      {/* Import Preview Dialog */}
      {showImportDialog && previewData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-zinc-800 border border-black rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">Import Preview</h3>
              <button
                onClick={() => setShowImportDialog(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-4">
              <p className="text-gray-300 text-sm">
                Found <span className="font-medium text-white">{previewData.totalRows}</span> tools to import
              </p>
            </div>

            {/* Duplicate Strategy */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Duplicate Handling Strategy
              </label>
              <select
                value={duplicateStrategy}
                onChange={(e) => setDuplicateStrategy(e.target.value as 'skip' | 'update' | 'error')}
                className="w-full px-3 py-2 bg-zinc-700 border border-gray-600 rounded-lg text-white"
              >
                <option value="skip">Skip duplicates</option>
                <option value="error">Error on duplicates</option>
                <option value="update">Update existing (not implemented)</option>
              </select>
            </div>

            {/* Sample Data Preview */}
            {previewData.sampleData && previewData.sampleData.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-300 mb-2">Sample Data (first 3 rows)</h4>
                <div className="bg-zinc-900 rounded-lg p-3 text-xs overflow-x-auto">
                  <pre className="text-gray-300">
                    {JSON.stringify(previewData.sampleData.slice(0, 3), null, 2)}
                  </pre>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowImportDialog(false)}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleImportConfirm}
                disabled={isImporting}
                className="px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-orange-800 text-white rounded-lg transition-colors"
              >
                {isImporting ? 'Importing...' : 'Confirm Import'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
