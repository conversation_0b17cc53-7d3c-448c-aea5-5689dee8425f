#!/usr/bin/env tsx

/**
 * Apply Uniqueness Constraints Migration
 * 
 * This script applies the fixed migration 003 that adds uniqueness constraints
 * and enables the pg_trgm extension for similarity matching.
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

async function applyUniquenessConstraintsMigration() {
  console.log('🚀 Applying Uniqueness Constraints Migration (003)...');
  console.log('=' .repeat(60));

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.log('❌ Missing Supabase environment variables');
    console.log('   Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // Check if migration has already been applied
    console.log('📋 Checking if migration already completed...');

    const { data: migrationCheck } = await supabase
      .from('system_configuration')
      .select('config_value')
      .eq('config_key', 'migration_003_uniqueness_constraints_completed')
      .single();

    if (migrationCheck) {
      console.log('✅ Migration 003 (uniqueness constraints) has already been completed. Skipping...');
      return;
    }

    console.log('🔧 Reading migration file...');
    
    // Read the migration SQL file
    const migrationPath = join(process.cwd(), 'src/lib/database/migrations/003_add_uniqueness_constraints.sql');
    const migrationSql = readFileSync(migrationPath, 'utf-8');

    console.log('📝 Migration SQL loaded successfully');
    console.log(`   File size: ${migrationSql.length} characters`);

    // Note: Supabase doesn't support direct multi-statement SQL execution via RPC
    // The migration needs to be run manually in the Supabase SQL Editor
    console.log('\n⚠️  IMPORTANT: Manual Migration Required');
    console.log('   Due to Supabase limitations, this migration must be run manually.');
    console.log('   Please follow these steps:');
    console.log('');
    console.log('   1. Open your Supabase Dashboard');
    console.log('   2. Go to SQL Editor');
    console.log('   3. Copy and paste the following SQL:');
    console.log('');
    console.log('=' .repeat(80));
    console.log(migrationSql);
    console.log('=' .repeat(80));
    console.log('');
    console.log('   4. Execute the SQL');
    console.log('   5. Run this script again to mark the migration as completed');
    console.log('');

    // Ask user if they want to mark as completed
    console.log('❓ Have you successfully executed the SQL in Supabase? (y/N)');
    
    // For automated testing, we'll skip the interactive part
    if (process.env.NODE_ENV === 'test' || process.argv.includes('--auto-complete')) {
      console.log('🤖 Auto-completion mode detected, marking migration as completed...');
      await markMigrationCompleted(supabase);
    } else {
      console.log('');
      console.log('💡 To mark this migration as completed after manual execution, run:');
      console.log('   npm run db:migrate:003 -- --auto-complete');
    }

  } catch (error: any) {
    console.log(`❌ Migration failed: ${error.message}`);
    console.log('   Please check your database connection and permissions.');
  }

  console.log('');
}

async function markMigrationCompleted(supabase: any) {
  console.log('📝 Marking migration as completed...');
  
  const { error: markError } = await supabase
    .from('system_configuration')
    .upsert({
      config_key: 'migration_003_uniqueness_constraints_completed',
      config_value: { 
        completed_at: new Date().toISOString(), 
        version: '1.0.0',
        features: [
          'pg_trgm extension enabled',
          'unique constraints for names, slugs, websites',
          'performance indexes for search',
          'validation functions',
          'automatic slug generation',
          'duplicate detection view'
        ]
      },
      config_type: 'system',
      description: 'Uniqueness constraints and pg_trgm extension migration completion marker'
    }, { onConflict: 'config_key' });

  if (markError) {
    console.log('⚠️  Could not mark migration as completed:', markError.message);
  } else {
    console.log('✅ Migration marked as completed');
  }
}

// Run the migration
applyUniquenessConstraintsMigration().catch(console.error);
