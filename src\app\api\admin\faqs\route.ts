import { NextRequest, NextResponse } from 'next/server';
import { updateToolFAQs, addFAQToTool } from '@/lib/supabase';
import { validateApiKey } from '@/lib/auth';
import { FAQ } from '@/lib/types';

/**
 * PUT /api/admin/faqs
 * Update all FAQs for a specific tool (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { toolId, faqs }: { toolId: string; faqs: FAQ[] } = await request.json();

    // Validate required fields
    if (!toolId) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    if (!Array.isArray(faqs)) {
      return NextResponse.json(
        { success: false, error: 'FAQs must be an array' },
        { status: 400 }
      );
    }

    // Validate each FAQ
    for (const faq of faqs) {
      if (!faq.question || !faq.question.trim()) {
        return NextResponse.json(
          { success: false, error: 'All FAQs must have a question' },
          { status: 400 }
        );
      }

      if (!faq.answer || !faq.answer.trim()) {
        return NextResponse.json(
          { success: false, error: 'All FAQs must have an answer' },
          { status: 400 }
        );
      }
    }

    const updatedFaqs = await updateToolFAQs(toolId, faqs);

    return NextResponse.json({
      success: true,
      data: updatedFaqs,
    });
  } catch (error) {
    console.error('Error updating tool FAQs:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update FAQs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/faqs
 * Add a new FAQ to a specific tool (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { toolId, faq }: { toolId: string; faq: Partial<FAQ> } = await request.json();

    // Validate required fields
    if (!toolId) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    if (!faq.question || !faq.question.trim()) {
      return NextResponse.json(
        { success: false, error: 'Question is required' },
        { status: 400 }
      );
    }

    if (!faq.answer || !faq.answer.trim()) {
      return NextResponse.json(
        { success: false, error: 'Answer is required' },
        { status: 400 }
      );
    }

    // Validate question length
    if (faq.question.length < 5 || faq.question.length > 500) {
      return NextResponse.json(
        { success: false, error: 'Question must be between 5 and 500 characters' },
        { status: 400 }
      );
    }

    // Validate answer length
    if (faq.answer.length < 10 || faq.answer.length > 5000) {
      return NextResponse.json(
        { success: false, error: 'Answer must be between 10 and 5000 characters' },
        { status: 400 }
      );
    }

    // Validate priority if provided
    if (faq.priority !== undefined && (faq.priority < 0 || faq.priority > 10)) {
      return NextResponse.json(
        { success: false, error: 'Priority must be between 0 and 10' },
        { status: 400 }
      );
    }

    // Validate display order if provided
    if (faq.displayOrder !== undefined && faq.displayOrder < 0) {
      return NextResponse.json(
        { success: false, error: 'Display order must be 0 or greater' },
        { status: 400 }
      );
    }

    const updatedFaqs = await addFAQToTool(toolId, faq);

    return NextResponse.json({
      success: true,
      data: updatedFaqs,
    });
  } catch (error) {
    console.error('Error adding FAQ to tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to add FAQ' },
      { status: 500 }
    );
  }
}
