import { NextRequest, NextResponse } from 'next/server';
import { validateA<PERSON><PERSON><PERSON> } from '@/lib/auth';
import { supabaseAdmin } from '@/lib/supabase';
import { ContentStatus } from '@/lib/types';

/**
 * POST /api/admin/tools/bulk
 * Bulk operations for admin tools (status updates and deletions)
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, toolIds, status } = body;

    if (!action || !Array.isArray(toolIds) || toolIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid request: action and toolIds are required' },
        { status: 400 }
      );
    }

    // Validate tool IDs
    if (toolIds.some(id => typeof id !== 'string' || !id.trim())) {
      return NextResponse.json(
        { success: false, error: 'Invalid tool IDs provided' },
        { status: 400 }
      );
    }

    let result;
    let message;

    switch (action) {
      case 'updateStatus':
        result = await handleBulkStatusUpdate(toolIds, status);
        message = `Successfully updated status for ${result.count} tools`;
        break;
      
      case 'delete':
        result = await handleBulkDelete(toolIds);
        message = `Successfully deleted ${result.count} tools`;
        break;
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action. Supported actions: updateStatus, delete' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data: result,
      message,
    });

  } catch (error) {
    console.error('Bulk operation error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}

/**
 * Handle bulk status updates with transaction
 */
async function handleBulkStatusUpdate(toolIds: string[], status: ContentStatus) {
  console.log(`🔄 Bulk status update: ${toolIds.length} tools to "${status}"`);
  console.log(`📋 Tool IDs: ${toolIds.join(', ')}`);

  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  if (!status || !['draft', 'published', 'archived', 'under_review', 'approved', 'rejected'].includes(status)) {
    throw new Error('Invalid status provided');
  }

  // First, verify that the tool IDs exist in the database
  console.log('🔍 Verifying tool IDs exist in database...');
  const { data: existingTools, error: verifyError } = await supabaseAdmin
    .from('tools')
    .select('id, name, content_status')
    .in('id', toolIds);

  if (verifyError) {
    console.error('❌ Error verifying tool IDs:', verifyError);
    throw new Error(`Failed to verify tool IDs: ${verifyError.message}`);
  }

  console.log(`📊 Found ${existingTools?.length || 0} tools in database:`);
  existingTools?.forEach(tool => {
    console.log(`   - ${tool.name} (${tool.id}): current status = ${tool.content_status}`);
  });

  if (!existingTools || existingTools.length === 0) {
    throw new Error(`No tools found with the provided IDs: ${toolIds.join(', ')}`);
  }

  if (existingTools.length !== toolIds.length) {
    const foundIds = existingTools.map(t => t.id);
    const missingIds = toolIds.filter(id => !foundIds.includes(id));
    console.warn(`⚠️  Some tool IDs not found: ${missingIds.join(', ')}`);
  }

  // Check current database constraint support
  const supportedStatuses = ['draft', 'published', 'archived'];
  if (!supportedStatuses.includes(status)) {
    console.warn(`⚠️  Status "${status}" may not be supported by current database constraint`);
    console.warn(`   Supported statuses: ${supportedStatuses.join(', ')}`);
    console.warn(`   Extended statuses require database migration`);
  }

  const { data, error, count } = await supabaseAdmin
    .from('tools')
    .update({
      content_status: status,
      updated_at: new Date().toISOString()
    })
    .in('id', toolIds)
    .select('id, name, content_status');

  console.log(`📊 Supabase response:`, {
    error: error ? error.message : null,
    count,
    dataLength: data ? data.length : 0
  });

  if (error) {
    console.error('❌ Bulk status update error:', error);

    // Check for constraint violation
    if (error.message.includes('check constraint') || error.message.includes('violates')) {
      throw new Error(`Database constraint violation: The status "${status}" is not supported by the current database schema. Please apply the database migration to support extended status values.`);
    }

    throw new Error(`Failed to update tool status: ${error.message}`);
  }

  // Verify the update actually worked
  if (!data || data.length === 0) {
    console.warn(`⚠️  Update returned no data - count=${count}, data length=${data ? data.length : 0}`);
    throw new Error('No tools were updated. This may indicate a constraint violation or permission issue.');
  }

  const actualCount = data.length;
  console.log(`✅ Successfully updated ${actualCount} tools to "${status}"`);
  data.forEach(tool => {
    console.log(`   - ${tool.name} (${tool.id}): ${tool.content_status}`);
  });

  return {
    count: actualCount,
    updatedTools: data || [],
    status
  };
}

/**
 * Handle bulk delete with transaction
 */
async function handleBulkDelete(toolIds: string[]) {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  // First, get the tools that will be deleted for logging
  const { data: toolsToDelete, error: fetchError } = await supabaseAdmin
    .from('tools')
    .select('id, name')
    .in('id', toolIds);

  if (fetchError) {
    console.error('Error fetching tools for deletion:', fetchError);
    throw new Error(`Failed to fetch tools for deletion: ${fetchError.message}`);
  }

  // Perform the bulk delete
  const { error: deleteError, count } = await supabaseAdmin
    .from('tools')
    .delete()
    .in('id', toolIds);

  if (deleteError) {
    console.error('Bulk delete error:', deleteError);
    throw new Error(`Failed to delete tools: ${deleteError.message}`);
  }

  return {
    count: count || 0,
    deletedTools: toolsToDelete || []
  };
}
