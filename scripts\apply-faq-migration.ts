#!/usr/bin/env tsx

/**
 * Apply FAQ System Migration Script
 * 
 * This script applies the simplified FAQ system migration that adds a JSONB column
 * to the tools table instead of creating a separate faqs table.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
config({ path: '.env.local' });

// Load environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  console.log('🚀 Starting FAQ System Migration (Simplified JSONB)...\n');

  try {
    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'src/lib/database/migrations/002_faq_system_schema.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Migration file loaded successfully');
    console.log('📝 Migration content:');
    console.log('---');
    console.log(migrationSQL);
    console.log('---\n');

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📋 Found ${statements.length} SQL statements to execute\n`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}:`);
      console.log(`   ${statement.substring(0, 100)}${statement.length > 100 ? '...' : ''}`);

      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        // Try direct query execution as fallback
        const { error: directError } = await supabase.from('_').select('*').limit(0);
        
        if (directError) {
          console.error(`❌ Failed to execute statement ${i + 1}:`, error.message);
          throw error;
        }
      }

      console.log(`✅ Statement ${i + 1} executed successfully`);
    }

    console.log('\n🎉 Migration completed successfully!');

    // Verify the migration
    console.log('\n🔍 Verifying migration...');
    
    // Check if faqs column exists
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_name', 'tools')
      .eq('column_name', 'faqs');

    if (columnsError) {
      console.warn('⚠️  Could not verify column creation:', columnsError.message);
    } else if (columns && columns.length > 0) {
      console.log('✅ FAQs column successfully added to tools table');
      console.log(`   Column type: ${columns[0].data_type}`);
    } else {
      console.warn('⚠️  FAQs column not found in tools table');
    }

    // Check if any tools have sample FAQs
    const { data: toolsWithFaqs, error: toolsError } = await supabase
      .from('tools')
      .select('id, name, faqs')
      .not('faqs', 'is', null)
      .limit(5);

    if (toolsError) {
      console.warn('⚠️  Could not check for sample FAQs:', toolsError.message);
    } else if (toolsWithFaqs && toolsWithFaqs.length > 0) {
      console.log(`✅ Found ${toolsWithFaqs.length} tools with sample FAQs`);
      toolsWithFaqs.forEach(tool => {
        const faqCount = Array.isArray(tool.faqs) ? tool.faqs.length : 0;
        console.log(`   - ${tool.name}: ${faqCount} FAQ(s)`);
      });
    } else {
      console.log('ℹ️  No tools with FAQs found (this is normal for a fresh migration)');
    }

    console.log('\n✨ FAQ System Migration completed successfully!');
    console.log('📋 Next steps:');
    console.log('   1. Test the FAQ functionality in the admin panel');
    console.log('   2. Add FAQs to existing tools');
    console.log('   3. Verify the ToolQASection component displays FAQs correctly');

  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Check your database connection');
    console.error('   2. Verify you have the correct permissions');
    console.error('   3. Check the migration SQL syntax');
    console.error('   4. Review the Supabase logs for more details');
    process.exit(1);
  }
}

// Execute the migration
if (require.main === module) {
  applyMigration().catch(console.error);
}

export { applyMigration };
