-- FAQ System Database Schema Migration (Simplified)
-- Adds faqs JSONB column to existing tools table
-- Following existing database patterns and JSONB conventions

-- Add faqs JSONB column to tools table
ALTER TABLE tools ADD COLUMN IF NOT EXISTS faqs JSONB;

-- Create GIN index for JSONB FAQ search and filtering
CREATE INDEX IF NOT EXISTS idx_tools_faqs_gin ON tools USING gin(faqs);

-- Add comment for documentation
COMMENT ON COLUMN tools.faqs IS 'JSONB array storing frequently asked questions with structure: [{"question": "...", "answer": "...", "category": "general|pricing|features|support|getting-started", "displayOrder": 0, "priority": 5, "isActive": true, "isFeatured": false, "source": "manual|ai_generated|scraped|user_submitted", "helpScore": 0, "viewCount": 0}]';

-- Insert sample FAQ data for testing (optional)
-- This can be removed in production
UPDATE tools
SET faqs = jsonb_build_array(
  jsonb_build_object(
    'question', 'What is ' || name || '?',
    'answer', COALESCE(detailed_description, description, name || ' is an AI-powered tool designed to help users with various tasks.'),
    'category', 'general',
    'displayOrder', 0,
    'priority', 5,
    'isActive', true,
    'isFeatured', false,
    'source', 'ai_generated',
    'helpScore', 0,
    'viewCount', 0
  )
)
WHERE content_status = 'published'
  AND (faqs IS NULL OR jsonb_array_length(faqs) = 0)
  AND name IS NOT NULL
  AND description IS NOT NULL;
