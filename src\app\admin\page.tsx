'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { apiClient } from '@/lib/api';
import { AITool, DbTool, AICategory } from '@/lib/types';
import { AdminDashboardStats } from '@/components/admin/AdminDashboardStats';

export default function AdminPanel() {
  const router = useRouter();
  const [tools, setTools] = useState<DbTool[]>([]);
  const [categories, setCategories] = useState<AICategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeJobs, setActiveJobs] = useState<number>(0);
  const [pendingReviews, setPendingReviews] = useState<number>(0);

  useEffect(() => {
    loadData();
  }, []);

  const fetchActiveJobs = async () => {
    try {
      const response = await fetch('/api/automation/jobs?status=active&limit=100', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || '',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data?.jobs) {
          // Count active jobs (running, waiting, delayed)
          const activeJobCount = result.data.jobs.filter((job: any) =>
            ['active', 'waiting', 'delayed', 'paused'].includes(job.status)
          ).length;
          setActiveJobs(activeJobCount);
        }
      }
    } catch (error) {
      console.error('Failed to fetch active jobs:', error);
      setActiveJobs(0);
    }
  };

  const fetchPendingReviews = async () => {
    try {
      const response = await fetch('/api/editorial/review?action=queue&status=pending', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || '',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setPendingReviews(Array.isArray(result.data) ? result.data.length : 0);
        }
      }
    } catch (error) {
      console.error('Failed to fetch pending reviews:', error);
      setPendingReviews(0);
    }
  };

  const loadData = async () => {
    try {
      const [toolsResult, categoriesResult] = await Promise.all([
        apiClient.getAdminTools({ limit: 100 }, 'aidude_admin_2024_secure_key_xyz789'),
        apiClient.getCategories(),
        fetchActiveJobs(),
        fetchPendingReviews()
      ]);

      // Validate API response structure
      if (!toolsResult || !toolsResult.data || !Array.isArray(toolsResult.data)) {
        console.error('Invalid tools API response:', toolsResult);
        throw new Error('Invalid API response: tools data is missing or not an array');
      }

      if (!Array.isArray(categoriesResult)) {
        console.error('Invalid categories API response:', categoriesResult);
        throw new Error('Invalid API response: categories data is not an array');
      }

      // Transform AITool[] to DbTool[] for admin interface
      const dbTools: DbTool[] = toolsResult.data.map(tool => ({
        id: tool.id,
        name: tool.name,
        slug: tool.slug || '',
        logo_url: tool.logoUrl || '',
        description: tool.description || '',
        short_description: tool.shortDescription || '',
        detailed_description: tool.detailedDescription || '',
        link: tool.link,
        website: tool.website || '',
        category_id: tool.category || '',
        subcategory: tool.subcategory || '',
        company: tool.company || '',
        is_verified: tool.isVerified || false,
        is_claimed: tool.isClaimed || false,
        content_status: tool.contentStatus || 'draft',
        created_at: tool.createdAt || '',
        updated_at: tool.updatedAt || '',
        published_at: tool.publishedAt || '',
        // Enhanced AI System fields
        scraped_data: tool.scrapedData || null,
        ai_generation_status: tool.aiGenerationStatus || 'pending',
        last_scraped_at: tool.lastScrapedAt || '',
        editorial_review_id: tool.editorialReviewId || '',
        ai_generation_job_id: tool.aiGenerationJobId || '',
        submission_type: tool.submissionType || 'admin',
        submission_source: tool.submissionSource || '',
        content_quality_score: tool.contentQualityScore || undefined,
        last_ai_update: tool.lastAiUpdate || '',
        // Additional required DbTool fields
        features: tool.features || null,
        screenshots: tool.screenshots || null,
        pricing: tool.pricing || null,
        social_links: tool.socialLinks || null,
        pros_and_cons: tool.prosAndCons || null,
        haiku: tool.haiku || null,
        hashtags: tool.hashtags || null,
        releases: tool.releases || null,
        claim_info: tool.claimInfo || null,
        meta_title: tool.metaTitle || '',
        meta_description: tool.metaDescription || '',
        generated_content: tool.generatedContent || null,
      }));
      setTools(dbTools);
      setCategories(categoriesResult);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading admin panel...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Dashboard Overview</h1>
          <p className="text-gray-400 mt-1">Manage your AI tools directory</p>
        </div>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => router.push('/admin/tools')}
            className="text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
            style={{
              backgroundColor: 'rgb(255, 150, 0)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
            }}
          >
            🔧 Manage Tools
          </button>
          <button
            onClick={() => router.push('/admin/tools/new')}
            className="text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-zinc-700 hover:bg-zinc-600"
          >
            ➕ Add New Tool
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <AdminDashboardStats
        stats={{
          totalTools: tools.length,
          publishedTools: tools.filter(t => t.content_status === 'published').length,
          draftTools: tools.filter(t => t.content_status === 'draft' || !t.content_status).length,
          archivedTools: tools.filter(t => t.content_status === 'archived').length,
          activeJobs: activeJobs,
          pendingReviews: pendingReviews
        }}
      />

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg">
          <h3 className="text-lg font-semibold mb-4 text-white">Quick Actions</h3>
          <div className="space-y-2">
            <button
              onClick={() => router.push('/admin/tools')}
              className="w-full text-left px-3 py-2 rounded-lg bg-zinc-700 hover:bg-zinc-600 transition-colors text-white"
            >
              🔧 Manage Tools
            </button>
            <button
              onClick={() => router.push('/admin/tools/new')}
              className="w-full text-left px-3 py-2 rounded-lg bg-zinc-700 hover:bg-zinc-600 transition-colors text-white"
            >
              ➕ Add New Tool
            </button>
            <button
              onClick={() => router.push('/admin/bulk')}
              className="w-full text-left px-3 py-2 rounded-lg bg-zinc-700 hover:bg-zinc-600 transition-colors text-white"
            >
              📦 Bulk Processing
            </button>
          </div>
        </div>

        <div className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg">
          <h3 className="text-lg font-semibold mb-4 text-white">Recent Activity</h3>
          <div className="space-y-2 text-sm text-gray-400">
            <p>• 5 new tools added today</p>
            <p>• 12 tools published this week</p>
            <p>• 3 pending submissions</p>
          </div>
        </div>

        <div className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg">
          <h3 className="text-lg font-semibold mb-4 text-white">System Status</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-white">Database</span>
              <span className="text-green-400">✓ Online</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white">API</span>
              <span className="text-green-400">✓ Online</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white">Jobs</span>
              <span className="text-green-400">✓ Running</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
