/**
 * Admin Panel Type Definitions
 * 
 * This file contains TypeScript interfaces and types for the admin panel components
 * and functionality, following the established design system and architecture.
 */

import { ReactNode } from 'react';

// Navigation Types
export interface AdminNavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  path: string;
  children?: AdminNavigationItem[];
  badge?: string | number;
  isExternal?: boolean;
}

export interface AdminNavigationSection {
  id: string;
  title: string;
  items: AdminNavigationItem[];
}

// Layout Types
export interface AdminLayoutStructure {
  header: {
    logo: string;
    userMenu: AdminUserMenu;
    notifications: NotificationCenter;
    systemStatus: SystemStatusIndicator;
  };
  sidebar: {
    navigation: AdminNavigation;
    quickActions: QuickActionPanel;
    systemMetrics: LiveMetrics;
  };
  mainContent: {
    breadcrumbs: BreadcrumbNavigation;
    pageContent: DynamicContent;
    actionBar: ContextualActions;
  };
  footer: {
    systemInfo: SystemInformation;
    supportLinks: SupportLinks;
  };
}

// User Menu Types
export interface AdminUserMenu {
  user: AdminUser;
  menuItems: AdminMenuItem[];
  onLogout: () => void;
}

export interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: AdminRole;
  avatar?: string;
  lastLogin?: Date;
}

export interface AdminMenuItem {
  id: string;
  label: string;
  icon?: React.ComponentType<{ size?: number }>;
  path?: string;
  action?: () => void;
  divider?: boolean;
  variant?: 'default' | 'danger';
}

export type AdminRole = 'super_admin' | 'admin' | 'moderator' | 'editor';

// Notification Types
export interface NotificationCenter {
  notifications: AdminNotification[];
  unreadCount: number;
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead: () => void;
  onClearAll: () => void;
}

export interface AdminNotification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
  metadata?: Record<string, any>;
}

export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'system';

// System Status Types
export interface SystemStatusIndicator {
  status: SystemStatus;
  uptime: number;
  lastUpdate: Date;
  metrics: SystemMetrics;
}

export type SystemStatus = 'online' | 'maintenance' | 'degraded' | 'offline';

export interface SystemMetrics {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  activeJobs: number;
  queuedJobs: number;
  errorRate: number;
}

// Breadcrumb Types
export interface BreadcrumbNavigation {
  items: BreadcrumbItem[];
  separator?: ReactNode;
}

export interface BreadcrumbItem {
  label: string;
  path?: string;
  isActive: boolean;
  icon?: React.ComponentType<{ size?: number }>;
}

// Quick Actions Types
export interface QuickActionPanel {
  actions: QuickAction[];
  onActionClick: (actionId: string) => void;
}

export interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ size?: number }>;
  description?: string;
  shortcut?: string;
  variant?: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
}

// Live Metrics Types
export interface LiveMetrics {
  metrics: MetricItem[];
  refreshInterval: number;
  lastUpdate: Date;
}

export interface MetricItem {
  id: string;
  label: string;
  value: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: number;
  color?: 'green' | 'yellow' | 'red' | 'blue';
  icon?: React.ComponentType<{ size?: number }>;
}

// Content Types
export interface DynamicContent {
  title: string;
  description?: string;
  children: ReactNode;
  loading?: boolean;
  error?: string;
}

export interface ContextualActions {
  actions: ContextualAction[];
  onActionClick: (actionId: string) => void;
}

export interface ContextualAction {
  id: string;
  label: string;
  icon?: React.ComponentType<{ size?: number }>;
  variant?: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
  loading?: boolean;
  shortcut?: string;
}

// Footer Types
export interface SystemInformation {
  version: string;
  buildDate: Date;
  environment: 'development' | 'staging' | 'production';
  region?: string;
}

export interface SupportLinks {
  links: SupportLink[];
}

export interface SupportLink {
  id: string;
  label: string;
  url: string;
  isExternal: boolean;
  icon?: React.ComponentType<{ size?: number }>;
}

// Admin Navigation Types
export interface AdminNavigation {
  sections: AdminNavigationSection[];
  currentPath: string;
  onNavigate: (path: string) => void;
  collapsed?: boolean;
}

// Component Props Types
export interface AdminLayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
  actions?: ContextualAction[];
}

export interface AdminSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  currentPath: string;
  navigation: AdminNavigationItem[];
  collapsed?: boolean;
}

export interface AdminHeaderProps {
  onMenuClick: () => void;
  isSidebarOpen: boolean;
  user?: AdminUser;
  notifications?: AdminNotification[];
  systemStatus?: SystemStatus;
}

export interface AdminBreadcrumbsProps {
  currentPath: string;
  customItems?: BreadcrumbItem[];
}

// Authentication Types
export interface AdminAuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: AdminUser | null;
  error: string | null;
}

export interface AdminAuthActions {
  login: (credentials: AdminCredentials) => Promise<void>;
  logout: () => Promise<void>;
  validateAccess: () => Promise<boolean>;
  refreshToken: () => Promise<void>;
}

export interface AdminCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// Permission Types
export interface AdminPermissions {
  canManageTools: boolean;
  canManageUsers: boolean;
  canManageCategories: boolean;
  canViewAnalytics: boolean;
  canManageSystem: boolean;
  canModerateContent: boolean;
  canManageJobs: boolean;
  canAccessBulkOperations: boolean;
}

// Theme Types
export interface AdminTheme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}
