# Screenshots Field Implementation Guide

## Overview
Implementation guide for adding the `screenshots` JSONB field to the Add Tool Form as the highest priority enhancement.

## Database Schema
```sql
-- Field already exists in database
screenshots JSONB, -- Array of screenshot URLs
```

## TypeScript Interface Updates

### 1. Update Zod Schema
```typescript
// Add to addToolSchema in AddToolForm.tsx
screenshots: z.array(
  z.string().url('Please enter a valid screenshot URL')
).max(10, 'Maximum 10 screenshots allowed').optional(),
```

### 2. Update Form Data Type
```typescript
type AddToolFormData = z.infer<typeof addToolSchema>;
// screenshots will be automatically included as string[] | undefined
```

## UI Component Implementation

### 1. Screenshots Section Component
```tsx
{/* Screenshots Section */}
<div className="space-y-4">
  <h3 className="text-lg font-medium text-white border-b border-zinc-600 pb-2">
    Screenshots & Media
  </h3>
  
  <div>
    <label className="block text-sm font-medium text-gray-300 mb-2">
      Screenshots
    </label>
    <p className="text-xs text-gray-400 mb-3">
      Add up to 10 screenshot URLs to showcase your tool's interface and features
    </p>
    
    {/* Dynamic Screenshot Inputs */}
    <div className="space-y-3">
      {screenshotFields.map((field, index) => (
        <div key={field.id} className="flex items-center space-x-2">
          <div className="flex-1">
            <input
              type="url"
              {...register(`screenshots.${index}`)}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.screenshots?.[index] ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder={`Screenshot ${index + 1} URL`}
            />
            {errors.screenshots?.[index] && (
              <p className="mt-1 text-sm text-red-400">
                {errors.screenshots[index]?.message}
              </p>
            )}
          </div>
          
          {/* Remove Button */}
          {screenshotFields.length > 1 && (
            <button
              type="button"
              onClick={() => removeScreenshot(index)}
              className="p-2 text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded-md transition-colors"
              title="Remove screenshot"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      ))}
    </div>
    
    {/* Add Screenshot Button */}
    {screenshotFields.length < 10 && (
      <button
        type="button"
        onClick={() => appendScreenshot('')}
        className="mt-3 flex items-center space-x-2 px-3 py-2 text-sm text-orange-400 hover:text-orange-300 hover:bg-orange-900/20 rounded-md transition-colors"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        <span>Add Screenshot</span>
      </button>
    )}
    
    {/* Screenshot Preview */}
    {watchedScreenshots && watchedScreenshots.length > 0 && (
      <div className="mt-4">
        <h4 className="text-sm font-medium text-gray-300 mb-2">Preview</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {watchedScreenshots.filter(url => url && url.trim()).map((url, index) => (
            <div key={index} className="relative group">
              <img
                src={url}
                alt={`Screenshot ${index + 1}`}
                className="w-full h-24 object-cover rounded-md border border-zinc-600"
                onError={(e) => {
                  e.currentTarget.src = '/placeholder-screenshot.png';
                }}
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-md flex items-center justify-center">
                <span className="text-white text-xs">Screenshot {index + 1}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}
  </div>
</div>
```

### 2. Form Hook Implementation
```typescript
import { useFieldArray } from 'react-hook-form';

// Inside AddToolForm component
const {
  fields: screenshotFields,
  append: appendScreenshot,
  remove: removeScreenshot
} = useFieldArray({
  control,
  name: 'screenshots'
});

// Watch screenshots for preview
const watchedScreenshots = watch('screenshots');

// Initialize with one empty field
useEffect(() => {
  if (screenshotFields.length === 0) {
    appendScreenshot('');
  }
}, [screenshotFields.length, appendScreenshot]);
```

## Form Submission Updates

### 1. Data Processing
```typescript
const onSubmit = async (data: AddToolFormData) => {
  setIsSubmitting(true);
  setSubmitError(null);

  try {
    // Filter out empty screenshot URLs
    const filteredScreenshots = data.screenshots
      ?.filter(url => url && url.trim())
      ?.map(url => url.trim()) || [];

    // Prepare tool data for API
    const toolData = {
      ...data,
      screenshots: filteredScreenshots.length > 0 ? filteredScreenshots : null,
      // ... other field processing
    };

    // Submit to API
    const newTool = await apiClient.createAdminTool(toolData, adminApiKey);
    
    // ... success handling
  } catch (err) {
    // ... error handling
  }
};
```

## API Integration

### 1. Update createTool Function
```typescript
// In src/lib/supabase.ts
export async function createTool(toolData: Partial<AITool>): Promise<AITool> {
  // ... existing code

  // Ensure screenshots is properly formatted
  if (toolData.screenshots) {
    filteredData.screenshots = Array.isArray(toolData.screenshots) 
      ? toolData.screenshots 
      : [];
  }

  // ... rest of function
}
```

### 2. Update API Endpoint
```typescript
// In src/app/api/admin/tools/route.ts
export async function POST(request: NextRequest) {
  try {
    const toolData = await request.json();
    
    // Validate screenshots array
    if (toolData.screenshots && !Array.isArray(toolData.screenshots)) {
      return NextResponse.json(
        { success: false, error: 'Screenshots must be an array' },
        { status: 400 }
      );
    }

    // ... rest of endpoint
  } catch (error) {
    // ... error handling
  }
}
```

## Styling & UX Enhancements

### 1. Responsive Design
- Grid layout adapts to screen size
- Touch-friendly buttons on mobile
- Proper spacing and alignment

### 2. Visual Feedback
- Loading states for image previews
- Error states for invalid URLs
- Success indicators for valid images

### 3. Accessibility
- Proper ARIA labels
- Keyboard navigation support
- Screen reader friendly descriptions

## Testing Strategy

### 1. Unit Tests
- Validate URL format checking
- Test array manipulation (add/remove)
- Verify form submission data

### 2. Integration Tests
- Test API endpoint with screenshots data
- Verify database storage and retrieval
- Test form reset functionality

### 3. User Testing
- Form usability with multiple screenshots
- Image preview functionality
- Error handling scenarios

## Implementation Checklist

- [ ] Update Zod schema with screenshots validation
- [ ] Add useFieldArray hook for dynamic inputs
- [ ] Implement screenshots UI section
- [ ] Add image preview functionality
- [ ] Update form submission logic
- [ ] Test API integration
- [ ] Add error handling
- [ ] Implement responsive design
- [ ] Add accessibility features
- [ ] Write unit tests
- [ ] Conduct user testing

## Estimated Implementation Time
- **Development**: 2-3 days
- **Testing**: 1 day
- **Documentation**: 0.5 days
- **Total**: 3.5-4.5 days
