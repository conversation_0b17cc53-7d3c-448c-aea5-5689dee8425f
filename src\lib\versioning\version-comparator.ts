/**
 * Version Comparator
 * Handles comparison between different versions of tools
 */

import { supabaseAdmin } from '@/lib/supabase';
import { AITool } from '@/lib/types';
import {
  VersionDiff,
  DiffSummary,
  FieldChange,
  DiffMetadata,
  CompareVersionsRequest,
  VersionComparison,
  DbVersionComparison
} from '@/lib/types/versioning';

export class VersionComparator {

  /**
   * Compare two versions of a tool
   */
  async compareVersions(request: CompareVersionsRequest): Promise<VersionDiff> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const startTime = Date.now();

    try {
      // Check cache first
      const cachedComparison = await this.getCachedComparison(
        request.toolId,
        request.fromVersionNumber,
        request.toVersionNumber
      );

      if (cachedComparison) {
        return cachedComparison.comparisonData;
      }

      // Get both versions
      const [fromVersion, toVersion] = await Promise.all([
        this.getVersionData(request.toolId, request.fromVersionNumber),
        this.getVersionData(request.toolId, request.toVersionNumber)
      ]);

      if (!fromVersion || !toVersion) {
        throw new Error('One or both versions not found');
      }

      // Perform comparison
      const diff = this.performDeepComparison(
        fromVersion.version_data,
        toVersion.version_data,
        request.fromVersionNumber,
        request.toVersionNumber
      );

      // Add metadata
      const processingTime = Date.now() - startTime;
      diff.metadata = {
        comparedAt: new Date().toISOString(),
        fromVersion: request.fromVersionNumber,
        toVersion: request.toVersionNumber,
        algorithm: 'deep-object-diff',
        processingTime
      };

      // Cache the result
      await this.cacheComparison(
        request.toolId,
        fromVersion.id,
        toVersion.id,
        diff
      );

      return diff;

    } catch (error) {
      console.error('Version comparison failed:', error);
      throw new Error(`Comparison failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get version data by tool ID and version number
   */
  private async getVersionData(toolId: string, versionNumber: number) {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const { data, error } = await supabaseAdmin
      .from('tool_versions')
      .select('*')
      .eq('tool_id', toolId)
      .eq('version_number', versionNumber)
      .single();

    if (error) {
      throw new Error(`Failed to fetch version ${versionNumber}: ${error.message}`);
    }

    return data;
  }

  /**
   * Perform deep comparison between two tool objects
   */
  private performDeepComparison(
    fromData: AITool,
    toData: AITool,
    fromVersion: number,
    toVersion: number
  ): VersionDiff {
    const changes: FieldChange[] = [];
    const significantChanges: string[] = [];

    // Define significant fields that should be highlighted
    const significantFields = [
      'name', 'description', 'short_description', 'detailed_description',
      'website', 'category_id', 'pricing', 'features', 'is_verified'
    ];

    // Compare all fields
    this.compareObjects(fromData, toData, '', changes, significantFields, significantChanges);

    // Calculate summary
    const summary: DiffSummary = {
      totalChanges: changes.length,
      addedFields: changes.filter(c => c.type === 'added').length,
      modifiedFields: changes.filter(c => c.type === 'modified').length,
      removedFields: changes.filter(c => c.type === 'removed').length,
      significantChanges
    };

    return {
      summary,
      changes,
      metadata: {
        comparedAt: '',
        fromVersion,
        toVersion,
        algorithm: '',
        processingTime: 0
      }
    };
  }

  /**
   * Recursively compare objects and build change list
   */
  private compareObjects(
    fromObj: any,
    toObj: any,
    path: string,
    changes: FieldChange[],
    significantFields: string[],
    significantChanges: string[]
  ): void {
    // Get all unique keys from both objects
    const allKeys = new Set([
      ...Object.keys(fromObj || {}),
      ...Object.keys(toObj || {})
    ]);

    for (const key of allKeys) {
      const currentPath = path ? `${path}.${key}` : key;
      const fromValue = fromObj?.[key];
      const toValue = toObj?.[key];

      if (fromValue === undefined && toValue !== undefined) {
        // Field added
        changes.push({
          field: key,
          type: 'added',
          newValue: toValue,
          path: currentPath,
          significance: this.getFieldSignificance(key, significantFields)
        });

        if (significantFields.includes(key)) {
          significantChanges.push(`Added ${key}`);
        }

      } else if (fromValue !== undefined && toValue === undefined) {
        // Field removed
        changes.push({
          field: key,
          type: 'removed',
          oldValue: fromValue,
          path: currentPath,
          significance: this.getFieldSignificance(key, significantFields)
        });

        if (significantFields.includes(key)) {
          significantChanges.push(`Removed ${key}`);
        }

      } else if (fromValue !== toValue) {
        // Field modified
        if (typeof fromValue === 'object' && typeof toValue === 'object' && 
            fromValue !== null && toValue !== null) {
          // Recursively compare objects
          this.compareObjects(fromValue, toValue, currentPath, changes, significantFields, significantChanges);
        } else {
          // Simple value change
          changes.push({
            field: key,
            type: 'modified',
            oldValue: fromValue,
            newValue: toValue,
            path: currentPath,
            significance: this.getFieldSignificance(key, significantFields)
          });

          if (significantFields.includes(key)) {
            significantChanges.push(`Modified ${key}`);
          }
        }
      }
    }
  }

  /**
   * Determine field significance level
   */
  private getFieldSignificance(field: string, significantFields: string[]): 'low' | 'medium' | 'high' {
    if (significantFields.includes(field)) {
      return 'high';
    }

    // Medium significance fields
    const mediumFields = [
      'logo_url', 'screenshots', 'social_links', 'pros_and_cons',
      'hashtags', 'releases', 'faqs', 'meta_title', 'meta_description'
    ];

    if (mediumFields.includes(field)) {
      return 'medium';
    }

    return 'low';
  }

  /**
   * Get cached comparison if available
   */
  private async getCachedComparison(
    toolId: string,
    fromVersion: number,
    toVersion: number
  ): Promise<VersionComparison | null> {
    try {
      const { data, error } = await supabaseAdmin
        .from('version_comparisons')
        .select('*')
        .eq('tool_id', toolId)
        .gt('expires_at', new Date().toISOString())
        .or(`and(from_version_id.eq.${fromVersion},to_version_id.eq.${toVersion}),and(from_version_id.eq.${toVersion},to_version_id.eq.${fromVersion})`)
        .single();

      if (error || !data) {
        return null;
      }

      return this.transformDbComparisonToComparison(data);

    } catch (error) {
      // Cache miss is not an error
      return null;
    }
  }

  /**
   * Cache comparison result
   */
  private async cacheComparison(
    toolId: string,
    fromVersionId: string,
    toVersionId: string,
    diff: VersionDiff
  ): Promise<void> {
    if (!supabaseAdmin) {
      return; // Skip caching if no admin client
    }

    try {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // Cache for 24 hours

      const cacheData: Omit<DbVersionComparison, 'id' | 'created_at'> = {
        tool_id: toolId,
        from_version_id: fromVersionId,
        to_version_id: toVersionId,
        comparison_data: diff,
        expires_at: expiresAt.toISOString()
      };

      await supabaseAdmin
        .from('version_comparisons')
        .insert([cacheData]);

    } catch (error) {
      // Cache failure shouldn't break the operation
      console.warn('Failed to cache comparison:', error);
    }
  }

  /**
   * Clean up expired comparison cache
   */
  async cleanupExpiredComparisons(): Promise<number> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const { data, error } = await supabaseAdmin
      .from('version_comparisons')
      .delete()
      .lt('expires_at', new Date().toISOString())
      .select('id');

    if (error) {
      throw new Error(`Failed to cleanup expired comparisons: ${error.message}`);
    }

    return data?.length || 0;
  }

  /**
   * Get comparison history for a tool
   */
  async getComparisonHistory(
    toolId: string,
    limit: number = 20
  ): Promise<VersionComparison[]> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const { data, error } = await supabaseAdmin
      .from('version_comparisons')
      .select('*')
      .eq('tool_id', toolId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to fetch comparison history: ${error.message}`);
    }

    return data?.map(comp => this.transformDbComparisonToComparison(comp)) || [];
  }

  /**
   * Transform database comparison to application format
   */
  private transformDbComparisonToComparison(dbComparison: DbVersionComparison): VersionComparison {
    return {
      id: dbComparison.id,
      toolId: dbComparison.tool_id,
      fromVersionId: dbComparison.from_version_id,
      toVersionId: dbComparison.to_version_id,
      comparisonData: dbComparison.comparison_data,
      createdAt: dbComparison.created_at,
      expiresAt: dbComparison.expires_at
    };
  }

  /**
   * Generate human-readable summary of changes
   */
  generateChangeSummary(diff: VersionDiff): string {
    const { summary } = diff;
    
    if (summary.totalChanges === 0) {
      return 'No changes detected between versions';
    }

    const parts: string[] = [];

    if (summary.addedFields > 0) {
      parts.push(`${summary.addedFields} field${summary.addedFields > 1 ? 's' : ''} added`);
    }

    if (summary.modifiedFields > 0) {
      parts.push(`${summary.modifiedFields} field${summary.modifiedFields > 1 ? 's' : ''} modified`);
    }

    if (summary.removedFields > 0) {
      parts.push(`${summary.removedFields} field${summary.removedFields > 1 ? 's' : ''} removed`);
    }

    let result = parts.join(', ');

    if (summary.significantChanges.length > 0) {
      result += `. Significant changes: ${summary.significantChanges.join(', ')}`;
    }

    return result;
  }
}
