#!/usr/bin/env tsx

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function checkFAQColumn() {
  console.log('🔍 Checking FAQ column status...\n');

  try {
    // Try to query the faqs column directly to see if it exists
    const { data: testQuery, error: testError } = await supabase
      .from('tools')
      .select('id, faqs')
      .limit(1);

    if (testError) {
      if (testError.message.includes('column "faqs" does not exist')) {
        console.log('❌ FAQs column NOT found in tools table');
        console.log('   Migration needs to be applied');
        return false;
      } else {
        console.error('❌ Error checking column:', testError.message);
        return false;
      }
    }

    console.log('✅ FAQs column exists in tools table');
    console.log('   Test query successful');

    // Check for any tools with FAQs
    const { data: toolsWithFaqs, error: toolsError } = await supabase
      .from('tools')
      .select('id, name, faqs')
      .not('faqs', 'is', null)
      .limit(3);

    if (toolsError) {
      console.error('❌ Error checking tools with FAQs:', toolsError.message);
    } else {
      console.log(`\nℹ️  Found ${toolsWithFaqs?.length || 0} tools with FAQs`);
      if (toolsWithFaqs && toolsWithFaqs.length > 0) {
        toolsWithFaqs.forEach(tool => {
          const faqCount = Array.isArray(tool.faqs) ? tool.faqs.length : 0;
          console.log(`   - ${tool.name}: ${faqCount} FAQ(s)`);
        });
      }
    }

    return true;

  } catch (err) {
    console.error('❌ Unexpected error:', err);
    return false;
  }
}

checkFAQColumn().catch(console.error);
