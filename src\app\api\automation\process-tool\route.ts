import { NextRequest, NextResponse } from 'next/server';
import { getJobManager } from '@/lib/jobs';
import { JobType, JobPriority } from '@/lib/jobs/types';
import { validate<PERSON>pi<PERSON>ey } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { url, name, description, category, submitterEmail, submitterName, priority = 'normal' } = await request.json();

    if (!url || !name || !submitterEmail) {
      return NextResponse.json(
        { success: false, error: 'URL, name, and submitter email are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(submitterEmail)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Use enhanced job system for better monitoring
    const jobManager = getJobManager();

    // Create tool submission job with enhanced features
    const job = await jobManager.createJob(
      JobType.TOOL_SUBMISSION,
      {
        url,
        name,
        description,
        category,
        submitterEmail,
        submitterName,
      },
      {
        priority: priority === 'high' ? JobPriority.HIGH :
                 priority === 'urgent' ? JobPriority.URGENT :
                 priority === 'low' ? JobPriority.LOW : JobPriority.NORMAL,
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        jobId: job.id,
        message: 'Tool processing started',
        status: job.status,
        estimatedTime: '2-5 minutes',
      },
    });
  } catch (error) {
    console.error('Error starting tool processing:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to start tool processing' },
      { status: 500 }
    );
  }
}
