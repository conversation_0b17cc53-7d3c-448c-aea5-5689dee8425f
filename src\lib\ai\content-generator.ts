import { OpenAIClient } from './providers/openai-client';
import { OpenRouterClient } from './providers/openrouter-client';
import { ModelSelector } from './model-selector';
import { PromptManager } from './prompt-manager';
import { ContextWindowManager } from './context-window-manager';
import {
  GenerationOptions,
  GenerationResult,
  GeneratedContent,
  ValidationResult,
  ModelConfig,
  AIResponse,
  MultiPromptContext
} from './types';
import { log } from '@/lib/logging';

export class AIContentGenerator {
  private openaiClient: OpenAIClient;
  private openrouterClient: OpenRouterClient;

  constructor() {
    this.openaiClient = new OpenAIClient();
    this.openrouterClient = new OpenRouterClient();
  }

  /**
   * Main content generation method with intelligent model selection and fallback
   */
  async generateContent(
    scrapedContent: string,
    toolUrl: string,
    options: GenerationOptions = {}
  ): Promise<GenerationResult> {
    try {
      // Optimize content for AI processing
      const processedContent = ContextWindowManager.optimizeContentForAI(
        scrapedContent,
        options.contentQuality,
        options.scrapingCost
      );

      // Select optimal model based on content characteristics
      const modelConfig = ModelSelector.selectOptimalModel({
        contentSize: processedContent.estimatedTokens,
        complexity: options.complexity || 'medium',
        priority: options.priority || 'quality',
        features: options.features || [],
        scrapingCost: options.scrapingCost,
        contentQuality: options.contentQuality
      });

      log.ai('model-selection', `Selected model: ${modelConfig.model} (${modelConfig.provider}) - ${modelConfig.reasoning}`, {
        toolUrl,
        model: modelConfig.model,
        provider: modelConfig.provider
      });

      // Generate content with fallback strategies
      const result = await this.generateContentWithFallback(
        processedContent.optimizedContent,
        toolUrl,
        modelConfig,
        options.maxRetries || 3
      );

      // Validate generated content
      const validation = await this.validateGeneratedContent(result.content);

      return {
        success: true,
        content: result.content,
        validation,
        modelUsed: modelConfig,
        tokenUsage: result.tokenUsage,
        timestamp: new Date().toISOString(),
        strategyUsed: result.strategyUsed
      };

    } catch (error: any) {
      log.error('Content generation failed', error, {
        component: 'ai-system',
        operation: 'generate-content',
        toolUrl
      });

      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Generate content with multiple fallback strategies
   */
  private async generateContentWithFallback(
    content: string,
    toolUrl: string,
    primaryModel: ModelConfig,
    maxRetries: number
  ): Promise<{ content: GeneratedContent; tokenUsage?: any; strategyUsed: number }> {
    const strategies = [
      // Strategy 1: Primary model with optimal settings
      () => this.generateWithModel(content, toolUrl, primaryModel),
      
      // Strategy 2: Fallback model
      () => {
        const fallbackModel = ModelSelector.getFallbackModel(primaryModel, 'Primary model failed');
        return this.generateWithModel(content, toolUrl, fallbackModel);
      },
      
      // Strategy 3: Simplified generation with basic content
      () => this.generateBasicContent(content, toolUrl)
    ];

    for (const [index, strategy] of strategies.entries()) {
      try {
        log.ai('fallback-strategy', `Attempting strategy ${index + 1}`, {
          strategy: index + 1,
          totalStrategies: strategies.length
        });
        const result = await this.retryWithBackoff(strategy, maxRetries);
        
        return {
          content: result.content,
          tokenUsage: result.tokenUsage,
          strategyUsed: index + 1
        };

      } catch (error: any) {
        log.warn(`Strategy ${index + 1} failed: ${error.message}`, {
          component: 'ai-system',
          operation: 'fallback-strategy',
          strategy: index + 1,
          error: error.message
        });

        if (index === strategies.length - 1) {
          throw new Error(`All generation strategies failed. Last error: ${error.message}`);
        }
      }
    }

    throw new Error('All generation strategies failed');
  }

  /**
   * Generate content using a specific model
   */
  private async generateWithModel(
    content: string,
    toolUrl: string,
    modelConfig: ModelConfig
  ): Promise<{ content: GeneratedContent; tokenUsage?: any }> {
    const client = modelConfig.provider === 'openai' ? this.openaiClient : this.openrouterClient;
    
    // Check if content needs to be split
    const canHandle = client.canHandleContent(ContextWindowManager.calculateTokenCount(content));
    
    if (canHandle) {
      // Single prompt processing
      return await this.generateSinglePrompt(content, toolUrl, client, modelConfig);
    } else {
      // Multi-prompt processing
      return await this.generateMultiplePrompts(content, toolUrl, client, modelConfig);
    }
  }

  /**
   * Generate content with single prompt
   */
  private async generateSinglePrompt(
    content: string,
    toolUrl: string,
    client: OpenAIClient | OpenRouterClient,
    modelConfig: ModelConfig
  ): Promise<{ content: GeneratedContent; tokenUsage?: any }> {
    const systemPrompt = PromptManager.SYSTEM_PROMPTS.toolAnalysis;
    const userPrompt = PromptManager.buildSinglePrompt(content, toolUrl);

    const response = await client.generateContent(systemPrompt, userPrompt, {
      model: modelConfig.model,
      responseFormat: 'json_object',
      maxTokens: Math.floor(modelConfig.maxTokens * 0.8)
    });

    const parsedContent = PromptManager.extractJsonFromResponse(response.content);

    return {
      content: parsedContent,
      tokenUsage: response.tokenUsage
    };
  }

  /**
   * Generate content with multiple prompts for large content
   */
  private async generateMultiplePrompts(
    content: string,
    toolUrl: string,
    client: OpenAIClient | OpenRouterClient,
    modelConfig: ModelConfig
  ): Promise<{ content: GeneratedContent; tokenUsage?: any }> {
    const chunks = client.splitContentForModel(content);
    log.ai('multi-prompt', `Processing ${chunks.length} content chunks`, {
      chunkCount: chunks.length,
      model: modelConfig.model
    });

    const results = [];
    let accumulatedContext = '';
    const totalTokenUsage = { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const context: MultiPromptContext = {
        isFirstChunk: i === 0,
        isLastChunk: i === chunks.length - 1,
        chunkIndex: i,
        totalChunks: chunks.length,
        accumulatedContext,
        toolUrl
      };

      const systemPrompt = PromptManager.SYSTEM_PROMPTS.toolAnalysis;
      const userPrompt = PromptManager.buildMultiPrompt(chunk, context);

      const response = await client.generateContent(systemPrompt, userPrompt, {
        model: modelConfig.model,
        responseFormat: 'json_object',
        maxTokens: Math.floor(modelConfig.maxTokens * 0.8)
      });

      const parsedResult = PromptManager.extractJsonFromResponse(response.content);
      results.push(parsedResult);

      // Accumulate context for next chunk
      accumulatedContext += `\n\nChunk ${i + 1} result: ${JSON.stringify(parsedResult)}`;

      // Accumulate token usage
      if (response.tokenUsage) {
        totalTokenUsage.prompt_tokens += response.tokenUsage.prompt_tokens;
        totalTokenUsage.completion_tokens += response.tokenUsage.completion_tokens;
        totalTokenUsage.total_tokens += response.tokenUsage.total_tokens;
      }
    }

    // Combine results from all chunks
    const combinedContent = PromptManager.combineMultiPromptResults(results);

    return {
      content: combinedContent,
      tokenUsage: totalTokenUsage
    };
  }

  /**
   * Generate basic content as fallback
   */
  private async generateBasicContent(
    content: string,
    toolUrl: string
  ): Promise<{ content: GeneratedContent; tokenUsage?: any }> {
    // Use OpenAI with simplified prompt for basic content generation
    const simplifiedContent = content.substring(0, 10000); // Limit content size
    const basicPrompt = `Generate basic AI tool information for: ${toolUrl}

Content: ${simplifiedContent}

Provide JSON with: detailed_description, features (3-5 items), pricing type, pros_and_cons (3 each).`;

    const response = await this.openaiClient.generateContent(
      'You are an AI tool analyst. Generate basic tool information in JSON format.',
      basicPrompt,
      {
        model: 'gpt-4o-2024-11-20',
        responseFormat: 'json_object',
        maxTokens: 2000
      }
    );

    const parsedContent = PromptManager.extractJsonFromResponse(response.content);

    return {
      content: parsedContent,
      tokenUsage: response.tokenUsage
    };
  }

  /**
   * Validate generated content quality and completeness
   */
  private async validateGeneratedContent(content: GeneratedContent): Promise<ValidationResult> {
    const issues: string[] = [];

    // Check required fields
    const requiredFields = ['detailed_description', 'features', 'pricing', 'pros_and_cons'];
    for (const field of requiredFields) {
      if (!content[field as keyof GeneratedContent]) {
        issues.push(`Missing required field: ${field}`);
      }
    }

    // Validate content length
    if (content.detailed_description && content.detailed_description.length > 2000) {
      issues.push('Detailed description is too long (>2000 chars)');
    }

    if (content.detailed_description && content.detailed_description.length < 150) {
      issues.push('Detailed description is too short (<150 chars)');
    }

    // Validate features count
    if (content.features && (content.features.length < 3 || content.features.length > 8)) {
      issues.push('Features count should be between 3-8');
    }

    // Validate pricing type
    const validPricingTypes = ['Free', 'Paid', 'Freemium', 'Open Source'];
    if (content.pricing && !validPricingTypes.includes(content.pricing.type)) {
      issues.push('Invalid pricing type');
    }

    // Validate pros and cons
    if (content.pros_and_cons) {
      if (!content.pros_and_cons.pros || content.pros_and_cons.pros.length < 3) {
        issues.push('Need at least 3 pros');
      }
      if (!content.pros_and_cons.cons || content.pros_and_cons.cons.length < 3) {
        issues.push('Need at least 3 cons');
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      score: Math.max(0, 100 - (issues.length * 10)) // Quality score
    };
  }

  /**
   * Retry operation with exponential backoff
   */
  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;
        
        if (attempt === maxRetries) {
          break;
        }

        // Exponential backoff: 1s, 2s, 4s, 8s...
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 30000);
        log.ai('retry-attempt', `Attempt ${attempt} failed, retrying in ${delay}ms`, {
          attempt,
          maxRetries,
          delay,
          error: error.message
        });
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Get provider status and capabilities
   */
  async getProviderStatus(): Promise<{
    openai: { available: boolean; info: any };
    openrouter: { available: boolean; info: any };
  }> {
    const [openaiStatus, openrouterStatus] = await Promise.allSettled([
      this.openaiClient.validateConnection(),
      this.openrouterClient.validateConnection()
    ]);

    return {
      openai: {
        available: openaiStatus.status === 'fulfilled' && openaiStatus.value,
        info: this.openaiClient.getProviderInfo()
      },
      openrouter: {
        available: openrouterStatus.status === 'fulfilled' && openrouterStatus.value,
        info: this.openrouterClient.getProviderInfo()
      }
    };
  }
}
