import { getEnhancedJobQueue } from './enhanced-queue';
import { getJobManager } from './job-manager';
import { getProgressTracker } from './progress-tracker';
import { getWebSocketManager } from './websocket-manager';

let initialized = false;
let enhancedInitialized = false;

export function initializeJobQueue() {
  if (initialized) {
    return;
  }

  console.log('⚠️ Legacy job queue initialization skipped - using enhanced system');
  console.log('💡 Use initializeEnhancedJobQueue() for full functionality');
  initialized = true;
}

/**
 * Initialize the enhanced job processing system
 */
export function initializeEnhancedJobQueue() {
  if (enhancedInitialized) {
    return;
  }

  if (process.env.JOB_QUEUE_ENABLED !== 'true') {
    console.log('Enhanced job queue is disabled');
    return;
  }

  try {
    // Initialize all enhanced components
    const progressTracker = getProgressTracker();
    const webSocketManager = getWebSocketManager();
    const enhancedQueue = getEnhancedJobQueue();
    const jobManager = getJobManager();

    console.log('✅ Enhanced job processing system initialized successfully');

    // Log enhanced configuration
    console.log(`📊 Enhanced job system configuration:
- Max concurrent jobs: ${process.env.MAX_CONCURRENT_JOBS || '3'}
- Retry attempts: ${process.env.JOB_RETRY_ATTEMPTS || '3'}
- Database persistence: enabled
- Real-time progress tracking: enabled
- WebSocket updates: enabled
- Job control features: pause/resume/stop enabled
- Content generation: ${process.env.CONTENT_GENERATION_ENABLED === 'true' ? 'enabled' : 'disabled'}
- Email notifications: ${process.env.SMTP_USER ? 'enabled' : 'disabled'}`);

    enhancedInitialized = true;
  } catch (error) {
    console.error('❌ Failed to initialize enhanced job queue:', error);
  }
}

/**
 * Initialize both legacy and enhanced systems for backward compatibility
 */
export function initializeAllJobSystems() {
  initializeJobQueue();
  initializeEnhancedJobQueue();
}

/**
 * Check if enhanced job system is available
 */
export function isEnhancedJobSystemAvailable(): boolean {
  return enhancedInitialized && process.env.JOB_QUEUE_ENABLED === 'true';
}

// Auto-initialize in production
if (process.env.NODE_ENV === 'production') {
  initializeEnhancedJobQueue();
}
