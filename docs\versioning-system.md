# Tool Versioning System Documentation

## Overview

The Tool Versioning System provides comprehensive version control for AI tools with rollback capabilities, audit logging, and version comparison features. This system enables tracking of all modifications to tools, maintaining historical versions, and allowing administrators to rollback to previous versions when needed.

## Architecture

### Core Components

1. **VersionManager** (`src/lib/versioning/version-manager.ts`)
   - Main service for version management operations
   - Handles version creation, rollback, and history retrieval
   - Integrates with audit logging and comparison services

2. **VersionAuditLogger** (`src/lib/versioning/audit-logger.ts`)
   - Comprehensive audit logging for all versioning operations
   - Tracks user actions, IP addresses, and session information
   - Provides audit statistics and search capabilities

3. **VersionComparator** (`src/lib/versioning/version-comparator.ts`)
   - Handles comparison between different versions of tools
   - Generates detailed diff reports with significance levels
   - Caches comparison results for performance

### Database Schema

The system uses three main tables:

- **tool_versions**: Stores complete tool snapshots for each version
- **version_audit_log**: Tracks all versioning actions for audit trail
- **version_comparisons**: Caches version comparison results

### UI Components

Located in `src/components/admin/versioning/`:

- **VersionManagement**: Main container component
- **VersionHistory**: Displays version history with pagination
- **VersionStatistics**: Shows version analytics and insights
- **VersionComparison**: Side-by-side version comparison
- **RollbackDialog**: Guided rollback process with validation

## Installation

### 1. Run Database Migration

```bash
npm run db:migrate:versioning
```

This will:
- Create versioning tables and indexes
- Install rollback functions and triggers
- Initialize versions for existing tools
- Set up audit logging infrastructure

### 2. Environment Variables

Ensure you have the required Supabase environment variables:

```env
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Usage

### Creating Versions

Versions are automatically created when tools are updated through the admin interface:

```typescript
import { VersionManager } from '@/lib/versioning';

const versionManager = new VersionManager();

await versionManager.createVersion({
  toolId: 'tool-id',
  changeSummary: 'Updated pricing information',
  changeType: 'update',
  changeSource: 'admin_panel',
  createdBy: '<EMAIL>'
});
```

### Rolling Back

```typescript
await versionManager.rollbackToVersion({
  toolId: 'tool-id',
  targetVersionNumber: 5,
  reason: 'Reverting problematic changes',
  performedBy: '<EMAIL>'
});
```

### Comparing Versions

```typescript
import { VersionComparator } from '@/lib/versioning';

const comparator = new VersionComparator();

const diff = await comparator.compareVersions({
  toolId: 'tool-id',
  fromVersionNumber: 1,
  toVersionNumber: 2,
  includeMetadata: true
});
```

## API Endpoints

### Version Management

- `GET /api/admin/tools/[id]/versions` - Get version history
- `POST /api/admin/tools/[id]/versions` - Create new version
- `POST /api/admin/tools/[id]/versions/rollback` - Rollback to version
- `GET /api/admin/tools/[id]/versions/rollback/validate` - Validate rollback

### Version Comparison

- `GET /api/admin/tools/[id]/versions/compare` - Compare versions
- `DELETE /api/admin/tools/[id]/versions/compare` - Clean comparison cache

### Statistics

- `GET /api/admin/tools/[id]/versions/statistics` - Get version statistics

## Admin Interface Integration

The versioning system is integrated into the tool edit form:

1. **Version Creation Toggle**: Option to create version on save
2. **Version Management Tab**: Access to full versioning interface
3. **Rollback Capabilities**: Guided rollback with validation
4. **Version Comparison**: Visual diff between versions

## Security Features

- **Authentication**: All operations require admin API key
- **Audit Logging**: Complete trail of all versioning actions
- **Validation**: Rollback validation prevents invalid operations
- **Transaction Safety**: Atomic operations with rollback on failure

## Performance Considerations

- **Snapshot Storage**: Uses JSONB for efficient storage and querying
- **Comparison Caching**: Results cached for 24 hours
- **Pagination**: Version history supports pagination
- **Cleanup**: Automatic cleanup of expired cache entries

## Monitoring and Maintenance

### Version Statistics

Monitor version activity through the statistics dashboard:
- Total versions per tool
- Change frequency analysis
- Top contributors
- Recent activity

### Cleanup Operations

Regular maintenance tasks:

```typescript
// Clean up expired comparison cache
await versionComparator.cleanupExpiredComparisons();

// Clean up old audit logs (90 days retention)
await auditLogger.cleanupOldLogs(90);
```

## Testing

Comprehensive Jest tests are included:

```bash
npm test src/__tests__/versioning/
```

Tests cover:
- Version creation and rollback
- Comparison functionality
- Audit logging
- Error handling
- Edge cases

## Troubleshooting

### Common Issues

1. **Migration Fails**: Check Supabase permissions and connection
2. **Version Creation Fails**: Verify tool exists and data is valid
3. **Rollback Validation Fails**: Check target version exists
4. **Performance Issues**: Monitor comparison cache hit rates

### Debug Mode

Enable debug logging by setting:

```env
DEBUG=versioning:*
```

## Future Enhancements

Planned improvements:
- Version branching and merging
- Automated version cleanup policies
- Version approval workflows
- Integration with external version control systems
- Performance optimizations for large datasets

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review test cases for usage examples
3. Examine audit logs for operation details
4. Contact the development team
