// Mock modules before importing
jest.mock('../supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null })),
        })),
      })),
    })),
  },
}));

import {
  transformAIToolToDbTool,
  transformDbToolToAITool,
  transformFormDataToAITool,
  transformAIToolToFormData,
  validateFieldMappings
} from '../data-transformers';
import { AITool, DbTool } from '../types';

describe('Data Transformers', () => {
  const mockAITool: AITool = {
    id: 'test-tool-123',
    name: 'Test AI Tool',
    slug: 'test-ai-tool',
    logoUrl: 'https://example.com/logo.png',
    description: 'Test description',
    shortDescription: 'Short test description',
    detailedDescription: 'Detailed test description',
    link: '/tools/test-ai-tool',
    website: 'https://test-tool.com',
    category: 'ai-writing',
    subcategory: 'text-generation',
    company: 'Test Company',
    isVerified: false,
    isClaimed: false,
    features: ['Feature 1', 'Feature 2'],
    screenshots: ['https://example.com/screenshot1.png'],
    pricing: { type: 'freemium', plans: [] },
    socialLinks: { twitter: 'https://twitter.com/testtool' },
    prosAndCons: { pros: ['Pro 1'], cons: ['Con 1'] },
    hashtags: ['ai', 'tool'],
    metaTitle: 'Test Tool Meta Title',
    metaDescription: 'Test tool meta description',
    contentStatus: 'draft',
    submissionType: 'admin',
    submissionSource: 'admin_panel',
    aiGenerationStatus: 'pending',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  };

  const mockDbTool: DbTool = {
    id: 'test-tool-123',
    name: 'Test AI Tool',
    slug: 'test-ai-tool',
    logo_url: 'https://example.com/logo.png',
    description: 'Test description',
    short_description: 'Short test description',
    detailed_description: 'Detailed test description',
    link: '/tools/test-ai-tool',
    website: 'https://test-tool.com',
    category_id: 'ai-writing',
    subcategory: 'text-generation',
    company: 'Test Company',
    is_verified: false,
    is_claimed: false,
    features: ['Feature 1', 'Feature 2'],
    screenshots: ['https://example.com/screenshot1.png'],
    pricing: { type: 'freemium', plans: [] },
    social_links: { twitter: 'https://twitter.com/testtool' },
    pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] },
    hashtags: ['ai', 'tool'],
    meta_title: 'Test Tool Meta Title',
    meta_description: 'Test tool meta description',
    content_status: 'draft',
    submission_type: 'admin',
    submission_source: 'admin_panel',
    ai_generation_status: 'pending',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    published_at: undefined,
    releases: undefined,
    haiku: undefined,
    claim_info: undefined,
    scraped_data: undefined,
    last_scraped_at: undefined,
    editorial_review_id: undefined,
    ai_generation_job_id: undefined,
    content_quality_score: undefined,
    last_ai_update: undefined,
    generated_content: undefined,
  };

  describe('transformAIToolToDbTool', () => {
    it('correctly transforms camelCase to snake_case', () => {
      const result = transformAIToolToDbTool(mockAITool);

      expect(result.short_description).toBe(mockAITool.shortDescription);
      expect(result.detailed_description).toBe(mockAITool.detailedDescription);
      expect(result.logo_url).toBe(mockAITool.logoUrl);
      expect(result.category_id).toBe(mockAITool.category);
      expect(result.is_verified).toBe(mockAITool.isVerified);
      expect(result.is_claimed).toBe(mockAITool.isClaimed);
      expect(result.social_links).toBe(mockAITool.socialLinks);
      expect(result.pros_and_cons).toBe(mockAITool.prosAndCons);
      expect(result.meta_title).toBe(mockAITool.metaTitle);
      expect(result.meta_description).toBe(mockAITool.metaDescription);
      expect(result.content_status).toBe(mockAITool.contentStatus);
      expect(result.submission_type).toBe(mockAITool.submissionType);
      expect(result.submission_source).toBe(mockAITool.submissionSource);
      expect(result.ai_generation_status).toBe(mockAITool.aiGenerationStatus);
    });

    it('handles undefined values correctly', () => {
      const partialTool: Partial<AITool> = {
        name: 'Test Tool',
        description: 'Test description'
      };

      const result = transformAIToolToDbTool(partialTool);

      expect(result.name).toBe('Test Tool');
      expect(result.description).toBe('Test description');
      expect(result.short_description).toBeUndefined();
      expect(result.detailed_description).toBeUndefined();
    });
  });

  describe('transformDbToolToAITool', () => {
    it('correctly transforms snake_case to camelCase', () => {
      const result = transformDbToolToAITool(mockDbTool);

      expect(result.shortDescription).toBe(mockDbTool.short_description);
      expect(result.detailedDescription).toBe(mockDbTool.detailed_description);
      expect(result.logoUrl).toBe(mockDbTool.logo_url);
      expect(result.category).toBe(mockDbTool.category_id);
      expect(result.isVerified).toBe(mockDbTool.is_verified);
      expect(result.isClaimed).toBe(mockDbTool.is_claimed);
      expect(result.socialLinks).toBe(mockDbTool.social_links);
      expect(result.prosAndCons).toBe(mockDbTool.pros_and_cons);
      expect(result.metaTitle).toBe(mockDbTool.meta_title);
      expect(result.metaDescription).toBe(mockDbTool.meta_description);
      expect(result.contentStatus).toBe(mockDbTool.content_status);
      expect(result.submissionType).toBe(mockDbTool.submission_type);
      expect(result.submissionSource).toBe(mockDbTool.submission_source);
      expect(result.aiGenerationStatus).toBe(mockDbTool.ai_generation_status);
    });

    it('provides default values for missing fields', () => {
      const minimalDbTool: DbTool = {
        id: 'test',
        name: 'Test',
        slug: 'test',
        link: '/tools/test',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        published_at: undefined,
        releases: undefined,
        haiku: undefined,
        claim_info: undefined,
        scraped_data: undefined,
        last_scraped_at: undefined,
        editorial_review_id: undefined,
        ai_generation_job_id: undefined,
        content_quality_score: undefined,
        last_ai_update: undefined,
        generated_content: undefined,
      };

      const result = transformDbToolToAITool(minimalDbTool);

      expect(result.logoUrl).toBe('');
      expect(result.description).toBe('');
      expect(result.shortDescription).toBe('');
      expect(result.detailedDescription).toBe('');
      expect(result.website).toBe('');
      expect(result.category).toBe('');
      expect(result.isVerified).toBe(false);
      expect(result.isClaimed).toBe(false);
      expect(result.contentStatus).toBe('draft');
      expect(result.aiGenerationStatus).toBe('pending');
    });
  });

  describe('transformFormDataToAITool', () => {
    it('correctly transforms form data to AITool', () => {
      const formData = {
        name: 'Test Tool',
        slug: 'test-tool',
        logo_url: 'https://example.com/logo.png',
        description: 'Test description',
        short_description: 'Short description',
        detailed_description: 'Detailed description',
        link: '/tools/test-tool',
        website: 'https://test-tool.com',
        category_id: 'ai-writing',
        subcategory: 'text-generation',
        company: 'Test Company',
        is_verified: false,
        is_claimed: false,
        features: 'Feature 1\nFeature 2\nFeature 3',
        content_status: 'draft',
        submission_type: 'admin',
        submission_source: 'admin_panel',
        ai_generation_status: 'pending',
      };

      const result = transformFormDataToAITool(formData);

      expect(result.shortDescription).toBe(formData.short_description);
      expect(result.detailedDescription).toBe(formData.detailed_description);
      expect(result.logoUrl).toBe(formData.logo_url);
      expect(result.category).toBe(formData.category_id);
      expect(result.isVerified).toBe(formData.is_verified);
      expect(result.isClaimed).toBe(formData.is_claimed);
      expect(result.features).toEqual(['Feature 1', 'Feature 2', 'Feature 3']);
      expect(result.contentStatus).toBe(formData.content_status);
      expect(result.submissionType).toBe(formData.submission_type);
      expect(result.submissionSource).toBe(formData.submission_source);
      expect(result.aiGenerationStatus).toBe(formData.ai_generation_status);
    });

    it('handles features field parsing correctly', () => {
      const formDataWithStringFeatures = {
        name: 'Test Tool',
        features: 'Feature 1\nFeature 2\n\nFeature 3\n',
        category_id: 'ai-writing',
      };

      const result1 = transformFormDataToAITool(formDataWithStringFeatures);
      expect(result1.features).toEqual(['Feature 1', 'Feature 2', 'Feature 3']);

      const formDataWithArrayFeatures = {
        name: 'Test Tool',
        features: ['Feature 1', 'Feature 2'],
        category_id: 'ai-writing',
      };

      const result2 = transformFormDataToAITool(formDataWithArrayFeatures);
      expect(result2.features).toEqual(['Feature 1', 'Feature 2']);

      const formDataWithEmptyFeatures = {
        name: 'Test Tool',
        features: '',
        category_id: 'ai-writing',
      };

      const result3 = transformFormDataToAITool(formDataWithEmptyFeatures);
      expect(result3.features).toBeUndefined();
    });
  });

  describe('transformAIToolToFormData', () => {
    it('correctly transforms AITool to form data', () => {
      const result = transformAIToolToFormData(mockAITool);

      expect(result.short_description).toBe(mockAITool.shortDescription);
      expect(result.detailed_description).toBe(mockAITool.detailedDescription);
      expect(result.logo_url).toBe(mockAITool.logoUrl);
      expect(result.category_id).toBe(mockAITool.category);
      expect(result.is_verified).toBe(mockAITool.isVerified);
      expect(result.is_claimed).toBe(mockAITool.isClaimed);
      expect(result.features).toBe('Feature 1\nFeature 2');
      expect(result.content_status).toBe(mockAITool.contentStatus);
      expect(result.submission_type).toBe(mockAITool.submissionType);
      expect(result.submission_source).toBe(mockAITool.submissionSource);
      expect(result.ai_generation_status).toBe(mockAITool.aiGenerationStatus);
    });

    it('handles array features correctly', () => {
      const toolWithFeatures = {
        ...mockAITool,
        features: ['Feature A', 'Feature B', 'Feature C']
      };

      const result = transformAIToolToFormData(toolWithFeatures);
      expect(result.features).toBe('Feature A\nFeature B\nFeature C');
    });

    it('handles missing features correctly', () => {
      const toolWithoutFeatures = {
        ...mockAITool,
        features: undefined
      };

      const result = transformAIToolToFormData(toolWithoutFeatures);
      expect(result.features).toBe('');
    });
  });

  describe('validateFieldMappings', () => {
    it('validates successful field mappings', () => {
      const original = {
        name: 'Test Tool',
        description: 'Test description',
        short_description: 'Short description',
        detailed_description: 'Detailed description',
        category_id: 'ai-writing',
        link: '/tools/test-tool',
        website: 'https://test.com'
      };

      const transformed = {
        name: 'Test Tool',
        description: 'Test description',
        short_description: 'Short description', // Keep snake_case for validation
        detailed_description: 'Detailed description', // Keep snake_case for validation
        category_id: 'ai-writing', // Keep snake_case for validation
        link: '/tools/test-tool',
        website: 'https://test.com'
      };

      const result = validateFieldMappings(original, transformed);
      expect(result.isValid).toBe(true);
      expect(result.missingFields).toEqual([]);
    });

    it('detects missing critical fields', () => {
      const original = {
        name: 'Test Tool',
        description: 'Test description',
        short_description: 'Short description',
        category_id: 'ai-writing',
        link: '/tools/test-tool',
        website: 'https://test.com'
      };

      const transformed = {
        name: 'Test Tool',
        // Missing description and other fields
        category: 'ai-writing',
        link: '/tools/test-tool',
        website: 'https://test.com'
      };

      const result = validateFieldMappings(original, transformed);
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain('description');
      expect(result.missingFields).toContain('short_description');
    });
  });

  describe('Bidirectional transformation integrity', () => {
    it('maintains data integrity through round-trip transformations', () => {
      // AITool -> DbTool -> AITool
      const dbTool = transformAIToolToDbTool(mockAITool);
      const backToAITool = transformDbToolToAITool(dbTool as DbTool);

      // Core fields should be preserved
      expect(backToAITool.name).toBe(mockAITool.name);
      expect(backToAITool.description).toBe(mockAITool.description);
      expect(backToAITool.shortDescription).toBe(mockAITool.shortDescription);
      expect(backToAITool.detailedDescription).toBe(mockAITool.detailedDescription);
      expect(backToAITool.category).toBe(mockAITool.category);
      expect(backToAITool.isVerified).toBe(mockAITool.isVerified);
      expect(backToAITool.isClaimed).toBe(mockAITool.isClaimed);
    });

    it('maintains form data integrity through transformations', () => {
      // AITool -> FormData -> AITool
      const formData = transformAIToolToFormData(mockAITool);
      const backToAITool = transformFormDataToAITool(formData);

      // Core fields should be preserved
      expect(backToAITool.name).toBe(mockAITool.name);
      expect(backToAITool.description).toBe(mockAITool.description);
      expect(backToAITool.shortDescription).toBe(mockAITool.shortDescription);
      expect(backToAITool.detailedDescription).toBe(mockAITool.detailedDescription);
      expect(backToAITool.category).toBe(mockAITool.category);
      expect(backToAITool.features).toEqual(mockAITool.features);
    });
  });
});
