'use client';

import React from 'react';
import Image from 'next/image';

interface ResponsiveImageProps {
  src: string | undefined | null;
  alt: string;
  width: number;
  height: number;
  className?: string;
  priority?: boolean;
  fallbackSrc?: string;
}

export function ResponsiveImage({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  fallbackSrc = '/placeholder-logo.svg',
}: ResponsiveImageProps) {
  // Handle empty, null, or undefined src
  const hasValidSrc = src && src.trim() !== '';

  // Use fallback if no valid src
  const imageSrc = hasValidSrc ? src : fallbackSrc;

  return (
    <Image
      src={imageSrc!}
      alt={alt}
      width={width}
      height={height}
      className={`object-cover ${className}`}
      priority={priority}
      unoptimized={imageSrc!.includes('picsum.photos')}
      onError={(e) => {
        // Fallback to placeholder SVG on error
        const target = e.target as HTMLImageElement;
        if (target.src !== fallbackSrc) {
          target.src = fallbackSrc;
        }
      }}
    />
  );
}
