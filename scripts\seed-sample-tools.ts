#!/usr/bin/env tsx

/**
 * Database Seeding Script for Sample Tools
 * 
 * This script moves the sample tool data from src/lib/constants.ts
 * into the database for development and testing purposes.
 * 
 * Usage:
 *   npm run seed:tools
 *   or
 *   npx tsx scripts/seed-sample-tools.ts
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Sample tool data extracted from constants.ts
const SAMPLE_TOOLS = [
  {
    id: 'chatgpt',
    name: 'Chat<PERSON><PERSON>',
    slug: 'chatgpt',
    logo_url: 'https://picsum.photos/16/16?random=1',
    description: 'Advanced AI chatbot for conversations and content creation',
    short_description: 'Advanced AI chatbot for conversations and content creation',
    detailed_description: 'ChatGPT is a state-of-the-art conversational AI developed by OpenAI. Built on the GPT-4 architecture, it can engage in natural conversations, answer questions, help with writing, coding, analysis, and much more.',
    link: '/tools/chatgpt',
    website: 'https://chat.openai.com',
    category_id: 'writing-tools',
    subcategory: 'Conversational AI',
    company: 'OpenAI',
    is_verified: true,
    is_claimed: true,
    content_status: 'published',
    submission_type: 'admin',
    submission_source: 'seed_script',
    features: [
      'Natural language conversations',
      'Code generation and debugging',
      'Writing and editing assistance',
      'Research and analysis',
      'Multiple language support',
      'Context-aware responses'
    ],
    screenshots: [
      'https://picsum.photos/800/450?random=201',
      'https://picsum.photos/800/450?random=202',
      'https://picsum.photos/800/450?random=203'
    ],
    pricing: {
      type: 'freemium',
      plans: [
        {
          name: 'Free',
          price: '$0/month',
          features: ['Access to GPT-3.5', 'Standard response speed', 'Regular model availability']
        },
        {
          name: 'ChatGPT Plus',
          price: '$20/month',
          features: ['Access to GPT-4', 'Faster response times', 'Priority access during peak times']
        }
      ]
    },
    social_links: {
      twitter: 'https://twitter.com/openai'
    },
    pros_and_cons: {
      pros: [
        'Highly capable and versatile AI assistant',
        'Excellent for research and analysis tasks',
        'Strong coding and technical support',
        'Natural conversation flow'
      ],
      cons: [
        'Can sometimes provide outdated information',
        'May generate plausible-sounding but incorrect answers',
        'Limited real-time information access (free version)',
        'Usage limits on free tier'
      ]
    },
    releases: [
      {
        version: 'GPT-4 Turbo',
        date: '2024-01-15',
        notes: 'Enhanced performance with improved reasoning capabilities and faster response times.',
        isLatest: true
      },
      {
        version: 'GPT-4',
        date: '2023-03-14',
        notes: 'Major upgrade with multimodal capabilities and improved accuracy.',
        isLatest: false
      }
    ],
    meta_title: 'ChatGPT - Advanced AI Chatbot | AI Dude',
    meta_description: 'ChatGPT is an advanced AI chatbot for conversations, content creation, coding, and analysis. Try the most popular AI assistant today.',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'claude',
    name: 'Claude',
    slug: 'claude',
    logo_url: 'https://picsum.photos/16/16?random=2',
    description: 'Anthropic\'s AI assistant for helpful, harmless, and honest conversations',
    short_description: 'Anthropic\'s AI assistant for helpful, harmless, and honest conversations',
    detailed_description: 'Claude is an AI assistant created by Anthropic to be helpful, harmless, and honest. Built with Constitutional AI techniques, Claude aims to be more transparent about its limitations.',
    link: '/tools/claude',
    website: 'https://claude.ai',
    category_id: 'writing-tools',
    subcategory: 'Conversational AI',
    company: 'Anthropic',
    is_verified: false,
    is_claimed: false,
    content_status: 'published',
    submission_type: 'admin',
    submission_source: 'seed_script',
    features: [
      'Constitutional AI for safer responses',
      'Long-form conversation capabilities',
      'Code analysis and generation',
      'Document analysis and summarization',
      'Creative writing assistance',
      'Ethical reasoning and discussion'
    ],
    screenshots: [
      'https://picsum.photos/800/450?random=301',
      'https://picsum.photos/800/450?random=302',
      'https://picsum.photos/800/450?random=303'
    ],
    pricing: {
      type: 'freemium',
      plans: [
        {
          name: 'Free',
          price: '$0/month',
          features: ['Limited daily usage', 'Claude Instant access', 'Basic support']
        },
        {
          name: 'Pro',
          price: '$20/month',
          features: ['Higher usage limits', 'Claude 2 access', 'Priority support']
        }
      ]
    },
    social_links: {
      twitter: 'https://twitter.com/anthropicai'
    },
    pros_and_cons: {
      pros: [
        'Constitutional AI approach for safer responses',
        'Excellent at nuanced and ethical reasoning',
        'Strong document analysis capabilities',
        'Transparent about limitations and uncertainties'
      ],
      cons: [
        'Smaller user base compared to ChatGPT',
        'Limited availability in some regions',
        'Fewer integrations with third-party tools',
        'Can be overly cautious in responses'
      ]
    },
    releases: [
      {
        version: 'Claude 3',
        date: '2024-03-04',
        notes: 'Major upgrade with improved reasoning, coding abilities, and multimodal capabilities.',
        isLatest: true
      },
      {
        version: 'Claude 2.1',
        date: '2023-11-21',
        notes: 'Enhanced with 200K token context window and improved accuracy for long documents.',
        isLatest: false
      }
    ],
    meta_title: 'Claude - AI Assistant by Anthropic | AI Dude',
    meta_description: 'Claude is Anthropic\'s AI assistant designed to be helpful, harmless, and honest. Experience Constitutional AI technology.',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'midjourney',
    name: 'Midjourney',
    slug: 'midjourney',
    logo_url: 'https://picsum.photos/16/16?random=12',
    description: 'AI art generator creating stunning visual content',
    short_description: 'AI art generator creating stunning visual content',
    detailed_description: 'Midjourney is an independent research lab exploring new mediums of thought and expanding the imaginative powers of the human species. Their AI-powered platform generates high-quality images from text descriptions.',
    link: '/tools/midjourney',
    website: 'https://midjourney.com',
    category_id: 'image-generators',
    subcategory: 'Text-to-Image',
    company: 'Midjourney Inc.',
    is_verified: true,
    is_claimed: true,
    content_status: 'published',
    submission_type: 'admin',
    submission_source: 'seed_script',
    features: [
      'High-quality AI image generation from text prompts',
      'Multiple artistic styles and aesthetics',
      'Advanced prompt engineering capabilities',
      'Community gallery and inspiration',
      'Commercial usage rights',
      'Fast generation times'
    ],
    screenshots: [
      'https://picsum.photos/800/450?random=101',
      'https://picsum.photos/800/450?random=102',
      'https://picsum.photos/800/450?random=103'
    ],
    pricing: {
      type: 'paid',
      plans: [
        {
          name: 'Basic Plan',
          price: '$10/month',
          features: ['200 image generations', 'General commercial terms', 'Access to member gallery']
        },
        {
          name: 'Standard Plan',
          price: '$30/month',
          features: ['Unlimited relaxed generations', '15 fast hours', 'Stealth mode', 'Commercial terms']
        }
      ]
    },
    social_links: {
      twitter: 'https://twitter.com/midjourney',
      github: 'https://github.com/midjourney'
    },
    pros_and_cons: {
      pros: [
        'Exceptional image quality and artistic detail',
        'Wide variety of artistic styles and aesthetics',
        'Strong community and inspiration gallery',
        'Fast generation times with reliable results'
      ],
      cons: [
        'Subscription-only pricing model',
        'Discord-based interface can be confusing',
        'Limited control over specific details',
        'Queue times during peak usage'
      ]
    },
    releases: [
      {
        version: 'V6',
        date: '2023-12-21',
        notes: 'Major update with improved photorealism, better text rendering, and enhanced prompt following capabilities.',
        isLatest: true
      },
      {
        version: 'V5.2',
        date: '2023-06-15',
        notes: 'Improved aesthetics, sharper images, and better understanding of prompts with enhanced zoom and variation features.',
        isLatest: false
      }
    ],
    meta_title: 'Midjourney - AI Art Generator | AI Dude',
    meta_description: 'Midjourney creates stunning AI-generated art from text prompts. Join millions of artists using the most popular AI image generator.',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

async function seedSampleTools() {
  console.log('🌱 Starting sample tools seeding...');
  
  try {
    // Check if tools already exist to avoid duplicates
    const { data: existingTools, error: checkError } = await supabase
      .from('tools')
      .select('id')
      .in('id', SAMPLE_TOOLS.map(tool => tool.id));

    if (checkError) {
      throw new Error(`Failed to check existing tools: ${checkError.message}`);
    }

    const existingIds = existingTools?.map(tool => tool.id) || [];
    const newTools = SAMPLE_TOOLS.filter(tool => !existingIds.includes(tool.id));

    if (newTools.length === 0) {
      console.log('✅ All sample tools already exist in database');
      return;
    }

    console.log(`📝 Inserting ${newTools.length} new sample tools...`);

    // Insert new tools
    const { data, error } = await supabase
      .from('tools')
      .insert(newTools)
      .select();

    if (error) {
      throw new Error(`Failed to insert tools: ${error.message}`);
    }

    console.log(`✅ Successfully seeded ${data?.length || 0} sample tools`);
    console.log('📋 Seeded tools:');
    data?.forEach(tool => {
      console.log(`   - ${tool.name} (${tool.id})`);
    });

  } catch (error) {
    console.error('❌ Error seeding sample tools:', error);
    process.exit(1);
  }
}

// Run the seeding script
if (require.main === module) {
  seedSampleTools()
    .then(() => {
      console.log('🎉 Sample tools seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Sample tools seeding failed:', error);
      process.exit(1);
    });
}

export { seedSampleTools, SAMPLE_TOOLS };
